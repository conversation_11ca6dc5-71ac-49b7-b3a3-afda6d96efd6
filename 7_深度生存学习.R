rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_new_seer1005.Rdata")
str(new_seer)
colnames(new_seer)

library(survivalmodels)
library(mlr3)
library(mlr3proba)
library(mlr3tuning)
library(magicfor)
library(mlr3learners)
library(mlr3extralearners)
library(mlr3verse)
library(paradox)
library(distr6)
library(dplyr)
######所有变量######
#vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
#"Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup","Grade",
#"Depth_Of_Invasion","Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
#"Surgery","Surgery_Prim_Site","Elective_Neck_Surgery","Surgery_Of_DX",
#"Chemotherapy","Radiotherapy","Adjuvant_Radiotherapy","Treatment","CSS",
#"Survival.months","Vital_status","One_year_status","One_year_status_data",
#"Three_year_status","Three_year_status_data","Five_year_status",
#"Five_year_status_data","Ten_year_status","Ten_year_status_data",
#"Recurrence_time","Recurrence_status") 
######确定研究的变量，包括自变量、结果变量和协变量######
#Survival_m 和 Three_y_Death 作为响应变量，其余变量作为预测变量
#因变量y（机器学习中，y被称为“标签”，自变量被称为‘特征’）

# extract data for three year cohort
table(new_seer$Three_year_status_data)
table(new_seer$Three_year_status)
threedata_full<-new_seer[new_seer$"Three_year_status_data" == 0,]
threedata_full<-as.data.frame(
  threedata_full[,c('Three_year_status','Survival.months',"Age","Sex",
                    "Marital_status","RaceGroup","household_income_group",
                    "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
                    "Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
                    "Surgery","Elective_Neck_Surgery","Surgery_Of_DX",
                    "Chemotherapy","Radiotherapy","Treatment")]
  )

threedata_full<-as.data.frame(
  threedata_full[,c('Three_year_status','Survival.months',
                    "Age","Sex","Grade","Stage_T")]
  )

threedata <-na.omit(threedata_full)
threedata<-droplevels(threedata)

#统计描述
library(psych)
summary(threedata)#Three_year_status的值需为0和1，注意因子化后值变为1和2
str(threedata)
head(threedata)
describe(threedata)
table(threedata$Three_year_status)
#threedata$Three_year_status <- as.numeric(threedata$Three_year_status)
#统计缺失值
sum(is.na(threedata))
which(is.na(threedata))
#将数据集转换为mlr3可识别的任务
task = TaskSurv$new("surv_task_threedata", threedata, 
                             time = "Survival.months", 
                             event = "Three_year_status")
#指定评价指标
measure = msr("surv.cindex")
#重采样策略
resampling = rsmp("cv", folds= 3)



######因子编码######
# 查看高基数(即水平数>10)特征
names(which(lengths(task$levels()) > 10))
# 查看二进制特征
names(which(lengths(task$levels()) ==2))
#建立管道
learner = lrn("surv.deepsurv",frac = 0.3, early_stopping = TRUE, 
              epochs = 10, optimizer = "adam")
factor_pipeline = 
  po("removeconstants") %>>% #删除常量特征 
  po("collapsefactors", no_collapse_above_prevalence = 0.01) %>>% #折叠出现次数过少的级别 
  po("encodeimpact", #影响编码 
     affect_columns = selector_cardinality_greater_than(10), 
     id = "high_card_enc") %>>% 
  po("encode", method = "one-hot", #one-hot编码，类似于设置哑变量 
     affect_columns = selector_cardinality_greater_than(2), 
     id = "low_card_enc") %>>% 
  po("encode", method = "treatment", #二进制特征b编码，即0,1 
     affect_columns = selector_type("factor"), id = "binary_enc")
#将管道用于学习器，并转换类型为学习器
learner_code1 = as_learner(factor_pipeline %>>% learner)
#设置学习器id
learner_code1$id = "surv_deepsurv_learner_code1"
#另建一个只用独热编码的管道，用于比较
learner_one_code = as_learner(po("encode") %>>% learner)
#设置学习器id
learner_one_code$id = "surv_deepsurv_learner_one"
#比较基线模型、独热编码处理的模型、复杂处理模型
design <- benchmark_grid(task,learner_code1, resampling)
bmr = benchmark(design)
resample(task,learner_code1, resampling)
#输出聚合性能，性能差异有限
bmr$aggregate(measure = measure)[,.(learner_id,surv.cindex)]




##############DeepSurv#################
library(survivalmodels)
library(mlr3)
library(mlr3proba)
library(mlr3tuning)
library(magicfor)
library(mlr3learners)
library(mlr3extralearners)
library(mlr3verse)
library(paradox)
library(distr6)
#install.packages("mlr3extralearners")
#remotes::install_github("mlr-org/mlr3extralearners")
#remotes::install_github("mlr-org/mlr3proba")
library(reticulate)
# 安装 pycox 和 keras
# 安装 pycox，并选择性地安装 torch
#py_install("pycox", pip = TRUE, install_torch = TRUE)

# 安装 keras，并选择性地安装 tensorflow
#py_install("keras", pip = TRUE, install_tensorflow = TRUE)
set.seed(1234)

index <- sample(1:nrow(threedata), round(0.3*nrow(threedata)))
test_set <- threedata[-index,]
train_set <- threedata[index,]
sum(is.na(threedata$Survival.months))
sum(is.na(threedata$Three_year_status))

train_task <- TaskSurv$new("train_surv_task", train_set, 
                           time = "Survival.months", 
                           event = "Three_year_status")
test_task <- TaskSurv$new("test_surv_task", test_set, 
                          time = "Survival.months", 
                          event = "Three_year_status")
tasks <- list(train_task,test_task)
tasks <- train_task
print(tasks)

search_space <- ps(
  ## p_dbl for numeric valued parameters
  dropout = p_dbl(lower = 0, upper = 1),
  weight_decay = p_dbl(lower = 0, upper = 0.5),
  learning_rate = p_dbl(lower = 0, upper = 1),
  
  ## p_int for integer valued parameters
  nodes = p_int(lower = 1, upper = 32),
  k = p_int(lower = 1, upper = 4),
  .extra_trafo = function(x, param_set) {
    x$num_nodes = rep(x$nodes, x$k)
    x$nodes = x$k = NULL
    return(x)
  }
)
print(search_space)

library(mlr3tuning)

create_autotuner <- function(learner) {
  AutoTuner$new(
    learner = learner,
    search_space = search_space,
    resampling = rsmp("holdout"),
    measure = msr("surv.cindex"),
    terminator = trm("evals", n_evals = 2),
    tuner = tnr("random_search")
  )
}

## load learners
learners <- lrns(
  paste0("surv.", c( "coxtime","deephit", "deepsurv", "loghaz", "pchazard")),
  frac = 0.3, early_stopping = TRUE, epochs = 10, optimizer = "adam"
)


# apply our function
learners <- lapply(learners, create_autotuner)

library(mlr3pipelines)

create_pipeops <- function(learner) {
  po("encode") %>>% po("scale") %>>% po("learner", learner)
}

## apply our function
learners <- lapply(learners, create_pipeops)

## select holdout as the resampling strategy
resampling <- rsmp("cv", folds = 3)

## add KM and CPH
learners <- c(learners, lrns(c("surv.kaplan", "surv.coxph")))
design <- benchmark_grid(tasks, learners, resampling)

library(mlr3batchmark)
resample(tasks,learners,resampling)
bm <- mlr3::benchmark(design)


bm <- tryCatch({
  benchmark(design)
}, error = function(e) {
  message("Error: ", e$message)
  NULL
})
update.packages(ask = FALSE)

bm <- benchmark(design)

## Aggreggate with Harrell's C and Integrated Graf Score
msrs <- msrs(c("surv.cindex", "surv.graf"))
bm$aggregate(msrs)[, c(3, 4, 7, 8)]

library(mlr3benchmark)
library(PMCMRplus)

## create mlr3benchmark object
bma <- as_benchmark_aggr(bm)
## run global Friedman test
bma$friedman_test()

## load ggplot2 for autoplots
library(ggplot2)

## critical difference diagrams for IGS
autoplot(bma, meas = "cindex", type = "cd", ratio = 1/3, p.value = 0.1) 
