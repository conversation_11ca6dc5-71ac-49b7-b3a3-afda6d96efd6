rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_psm_data1005.Rdata")
load("Cox_psm_data_tongue1007.Rdata")

######所有变量：自变量、协变量、结果变量######
#vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
#        "Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup","Grade",
#        "Depth_Of_Invasion","Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
#        "Surgery","Surgery_Prim_Site","Elective_Neck_Surgery","Surgery_Of_DX",
#        "Chemotherapy","Radiotherapy","Adjuvant_Radiotherapy","Treatment","CSS",
#        "Survival.months","Vital_status","1_year_status","3_year_status",
#        "5_year_status","10_year_status","Recurrence_time","Recurrence_status") 

#formula1 <- Chemotherapy~Age+Sex+Marital_status+RaceGroup+household_income_group+
#  Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
#  Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
#  Surgery_Of_DX+Radiotherapy #协变量，去除生存/复发状态、生存/复发时间

#vars_Cox<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
#            "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
#            "Stage_T","Stage_N","Stage_M","Stage","Surgery",
#            "Elective_Neck_Surgery","Surgery_Of_DX","Chemotherapy",
#            "Radiotherapy","Survival.months","Vital_status") #自变量、协变量、结果变量

######rms包绘制列线图######
str(Cox_psm_data)
vars_nom<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
            "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
            "Stage_T","Stage_N","Stage_M","Stage","Surgery",
            "Elective_Neck_Surgery","Surgery_Of_DX","Chemotherapy",
            "Radiotherapy")
# 使用 lapply 生成频数表
tables_list <- lapply(vars_nom, function(var) {
  table(Cox_psm_data[[var]])
})
# 将结果命名
names(tables_list) <- vars_nom
# 打印结果
tables_list

library(rms)
#这里我们依据rms包中的cph函数获得的回归模型绘制列线图，选择1年、2年、3年的风险估计。
str(Cox_psm_data)
Cox_psm_data <- na.omit(Cox_psm_data)
ddist <- datadist(Cox_psm_data);
options(datadist='ddist')
mul_cox <- cph(Surv(Survival.months, Vital_status) ~ Chemotherapy+Age+Sex+
                 Marital_status+RaceGroup+household_income_group+
                 Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
                 Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
                 Surgery_Of_DX+Radiotherapy, data = Cox_psm_data, x = TRUE, 
               y = TRUE, surv = TRUE)
# 创建生存对象
sur <- Survival(mul_cox)
sum(is.na(Cox_psm_data))

# 定义生存函数
sur1 <- function(x) sur(12, x)   # 1年生存
sur2 <- function(x) sur(36, x)   # 3年生存
sur3 <- function(x) sur(60, x)   # 5年生存

# 绘制nomogram
nom <- nomogram(mul_cox, 
                fun = list(sur2,sur3),  
                fun.at = c(0.05, seq(0.1, 0.9, by = 0.05), 0.95),  
                funlabel = c("3 years survival","5 years survival"))  

plot(nom)
Predict(mul_cox, Age="50-59", Sex='Female',Marital_status="Married",
        RaceGroup="Others",household_income_group="40000-79999",
        Year_of_diagnosis="2010-2015",Primary_Site="Ventral_surface_of_tongue",
        Tumor_Size="011-020",Grade="III-IV",Surgery_Prim_Site="Local_excision",
        Treatment="Surgery_RT",fun=sur3)

formula_lp(nomogram = nom,power = 3,digits=6)

######lrm()函数构建逻辑回归预测模型######
summary(Cox_psm_data$Survival.months)
table(Cox_psm_data$Survival.months)
f2 <- psm(Surv(Survival.months, Vital_status) ~ Chemotherapy+Age+Sex+
            Marital_status+RaceGroup+household_income_group+
            Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
            Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
            Surgery_Of_DX+Radiotherapy, data = Cox_psm_data, dist='lognormal') 
med <- Quantile(f2) # 计算中位生存时间
surv <- Survival(f2) # 构建生存概率函数

## 绘制COX回归中位生存时间的Nomogram图
nom <- nomogram(f2, fun=function(x) med(lp=x),                
                funlabel="Median Survival Time")
plot(nom)

## 绘制COX回归生存概率的Nomogram图
## 注意lung数据的time是以天为单位
nom <- nomogram(f2, fun=list(function(x) med(lp=x),
                             function(x) surv(12, x), 
                             function(x) surv(24, x)),                
                funlabel=c("Median Survival Time",
                           "1-year Survival Probability",
                           "2-year Survival Probability"))
plot(nom, xfrac=.6,
     cex.axis = 1,
     cex.var = 1)


####regplot包绘制nomogram######
library(regplot)
a=regplot(mul_cox,
          #对观测2的六个指标在列线图上进行计分展示
          #预测3年和5年的死亡风险，此处单位是day
          title = 'Survival Nomogram',
          observation = Cox_psm_data[2, ],#显示一下第2个病例的生存概率
          plots = c('density','no plot'),#"no plot" "boxes" "bars" "spikes"
          failtime = c(36,60), # For survival models only
          odds = F,               # 是否显示死亡几率
          leftlabel = T, #是否显示左侧标签
          center = TRUE,
          prfail = TRUE, #cox回归中需要TRUE
          showP = T, #是否展示统计学差异
          droplines = F,#观测2示例计分是否画线 #用前面自己定义的颜色
          rank = "range",      # 根据统计学差异的显著性进行变量的排序
          interval="confidence",
          points = T) #在列线图上显示点

######Clibration plot######
table(Cox_psm_data$Survival.months)
f=cph(formula = Surv(Survival.months, Vital_status) ~ Chemotherapy+Age+Sex+
        Marital_status+RaceGroup+household_income_group+
        Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
        Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
        Surgery_Of_DX+Radiotherapy, data = Cox_psm_data, y=T,x=T,surv = T,
      na.action=na.delete,time.inc = 60) 

cal=calibrate(f, cmethod="KM", method="boot",u=60,m=60,B=1000) 

#参数m=50表示每组50个样本进行重复计算
#######
quartz(width = 8, height = 8)
par(mar=c(6,6,6,6),cex = 1.0)
plot(cal,
     lwd = 2,#error bar的粗细
     lty = 0,#error bar的类型，可以是0-6
     errbar.col = c("#2166AC"),
     bty = "l", #只画左边和下边框
     xlim = c(0.5,1),ylim= c(0.5,1),
     xlab = "Nomogram-prediced OS (%)",ylab = "Observed OS (%)",
     col = c("#2166AC"),
     cex.lab=1.2,cex.axis=1, cex.main=1.2, cex.sub=0.6)#字的大小
lines(cal[,c('mean.predicted',"KM")],
      type = 'b',#连线的类型，可以是"p","b","o"
      lwd = 1, #连线的粗细 
      col = c("#2166AC"), 
      pch = 16)#点的形状，可以是0-20
mtext("")
box(lwd = 1) #边框粗细
abline(0,1, lty = 3,#对角线为虚线
       lwd = 2, #对角线的粗细
       col = c("#224444"))#对角线的颜色

legend("topleft", #图例的位置
       legend = c("5-year","8-year"), #图例文字
       col =c("#2166AC","#B2182B"), #图例线的颜色，与文字对应
       lwd = 2,#图例中线的粗细
       cex = 1.2,#图例字体大小
       bty = "n")#不显示图例边框
dev.off()