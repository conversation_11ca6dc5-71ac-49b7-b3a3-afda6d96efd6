rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_new_seer1005.Rdata")
str(new_seer)
colnames(new_seer)

######所有变量######
#vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
#        "Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup","Grade",
#        "Depth_Of_Invasion","Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
#        "Surgery","Surgery_Prim_Site","Elective_Neck_Surgery","Surgery_Of_DX",
#        "Chemotherapy","Radiotherapy","Adjuvant_Radiotherapy","Treatment","CSS",
#        "Survival.months","Vital_status","1_year_status","3_year_status",
#        "5_year_status","10_year_status","Recurrence_time","Recurrence_status") 
######确定研究的变量，包括自变量、结果变量和协变量######

######moonbook包制作临床三线图######
show_vars1<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
              "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
              "Stage_T","Stage_N","Stage_M","Stage","Surgery",
              "Elective_Neck_Surgery","Surgery_Of_DX","Chemotherapy",
              "Radiotherapy","Recurrence_status")#包括自变量和协变量
strata_vars <- c("Chemotherapy")
show_new_seer = new_seer[show_vars1]

library(moonBook)
str(show_new_seer)
summary(show_new_seer)
table_moonbook <- mytable(Chemotherapy~.,data=show_new_seer,digits=2,method=1,catMethod=0, show.total=TRUE)
table_moonbook <-mytable(Chemotherapy+Radiotherapy~.,data=show_new_seer,digits=2,method=1,catMethod=0)
str(table_moonbook)
mycsv(table_moonbook,file="baseline_moonbook.csv")

library(autoReg)
library(dplyr)
ft <- gaze(Chemotherapy~.,data=show_new_seer,digits=2,method=1,catMethod=0,show.p=TRUE) %>% 
  myft()
print(ft)
library(rrtable)
table2pptx(ft)
table2docx(ft)

######基线特征及差异性分析######
show_vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
             "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
             "Stage_T","Stage_N","Stage_M","Stage","Surgery",
             "Elective_Neck_Surgery","Surgery_Of_DX",
             "Radiotherapy","Recurrence_status")#协变量，去除自变量，结果变量
strata_vars <- c("Chemotherapy")#自变量
library(tableone)
library(MatchIt)
tab1 <- CreateTableOne(vars = show_vars, data = new_seer, factorVars = show_vars,
                       strata = strata_vars,smd = TRUE)
print(tab1, smd = TRUE, showAllLevels = TRUE)
tabMat<-print(tab1,showAllLevels=TRUE,noSpaces=TRUE,printToggle=FALSE)
write.csv(tabMat,file="tabMat_TableOne.csv")
library(officer)
doc <- read_docx()
tabMat_df <- as.data.frame(tabMat)
doc <- body_add_table(doc, value = tabMat_df, style = "table_template")
print(doc, target = "tabMat_TableOne.docx")

library(xtable)
library(flextable)
library(officer)
tabMat_flextable<-as_flextable(xtable(tabMat))
doc_flextable=read_docx()
doc_flextable =body_add_flextable(doc_flextable,tabMat_flextable)
print(doc_flextable,"tabMat_flextable.docx")

######table1包制作临床三线图######
library(table1)
colnames(show_new_seer)
table1(~Age+Sex+Marital_status+RaceGroup+household_income_group+
         Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
         Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
         Surgery_Of_DX+Radiotherapy+Recurrence_status|Chemotherapy,
       data=show_new_seer,topclass="Rtable1-zebra")

chisq.test(show_new_seer$Stage_N,show_new_seer$Chemotherapy)
## 加入p值
pvalue <- function(x, ...) { 
  y <- unlist(x)  
  g <- factor(rep(1:length(x), times=sapply(x, length)))
  if (is.numeric(y)) {    
    p <- t.test(y ~ g)$p.value #数值型数据用t-test(两组比较)  
  } else {    
    tbl <- table(y, g)
    if (any(tbl < 10)) {
      p <- fisher.test(tbl)$p.value # 使用 Fisher 检验
    } 
    else {    
      p <- chisq.test(table(y, g))$p.value #因子型数据用卡方  
    }  
  }
  c("", sub("<", "&lt;", format.pval(p, digits=3, eps=0.001)))
}

table1(~Age+Sex+Marital_status+RaceGroup+household_income_group+
         Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
         Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
         Surgery_Of_DX+Radiotherapy+Recurrence_status|Chemotherapy,
       data=new_seer,
       extra.col=list(`P-value`=pvalue), overall=F,topclass="Rtable1-zebra")

######gtsummary包制作三线图######
remotes::install_github("ddsjoberg/gtsummary")
library(gtsummary)
suppressPackageStartupMessages(library(tidyverse))

trial <- trial %>%
  mutate(trt = factor(trt, labels = c("A", "B")),
         stage = factor(stage, labels = c("I", "II", "III", "IV")))  # 示例数据

tbl_summary(trial)


str(show_new_seer)
tbl_summary(data = show_new_seer)
summary(show_new_seer)#检查缺失值
theme_gtsummary_journal(journal="jama")#使用JAMA主题
theme_gtsummary_compact()
tbl_summary(show_new_seer)
tbl_summary(show_new_seer, by = Chemotherapy)
library(stringr)  # 确保加载 stringr 包
show_new_seer %>%tbl_strata(
  strata = Grade,
  label = list(RaceGroup = "Race",
               household_income_group = "Household_income"),
  ~.x %>% 
    tbl_summary(by = Chemotherapy)%>%
    add_overall()%>%   # 添加 add_overall 函数以确保正确显示标签
    add_stat_label()  
) 

t1 <-show_new_seer %>%
  tbl_summary(by = Chemotherapy,
              label=list(Age~"Patient Age",
                         RaceGroup = "Race"),
              digits=list(all_continuous()~ 2,
                          all_categorical()~2)) %>%
  add_p(pvalue_fun =~style_pvalue(.x,digits=3)) %>%
  add_overall()%>%
  add_stat_label()
t1%>%
  as_hux_xlsx(file='table_1.xlsx')
t1 %>%
  as_flex_table()%>%
  flextable::save_as_docx(path='table_1.docx')
reset_gtsummary_theme()#清除主题

table(new_seer$Chemotherapy)

######根据treatment筛选数据，复活点new_seer2######
new_seer2=show_new_seer
show_new_seer=new_seer2
table(show_new_seer$Chemotherapy)
summary(is.na(show_new_seer$Chemotherapy))

######倾向性得分匹配(PSM)######
str(new_seer)
formula1 <- Chemotherapy~Age+Sex+Marital_status+RaceGroup+household_income_group+
  Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
  Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
  Surgery_Of_DX+Radiotherapy #协变量，去除生存/复发状态、生存/复发时间
#拟合逻辑回归模型
model<-glm(formula1,
           data=new_seer,family=binomial())
summary(is.na(new_seer))
summary(is.na(new_seer$Recurrence_time))
table(new_seer$Recurrence_status)
#计算倾向性评分
new_seer$propensity_score<-predict(model,type="response")
#查看倾向性评分的分布
library(ggplot2)
ggplot(new_seer,aes(x=propensity_score,y=after_stat(density),fill=strata_vars))+
  geom_histogram(bins=50,
                 alpha=0.5,colour="black",linewidth=0.025)+
  geom_density(linewidth=0.2,alpha=0.5)+
  scale_fill_manual(values=c("#D1BBDB","#BCE4B7"))+
  labs(title="Distribution of Propensity Scores",
       x="Propensity Score",#设置X轴标题
       y="density")#设置Y轴标题
#描述性统计
summary(new_seer$propensity_score)
#检查匹配前协变量平衡
library(MatchIt)
m_out_pre<-matchit(formula1, data =new_seer, method = NULL,
                   distance="glm",ratio=1,replace=FALSE)
plot(summary(m_out_pre))

#进行倾向性得分匹配(PSM),最近邻匹配（Nearest Neighbor Matching）
#glm可由new_seer$propensity_score代替
m_out<-matchit(formula1, data =new_seer, method = "nearest",
               distance="glm", m.order= "random",ratio=1,caliper=0.2,replace=FALSE)
plot(summary(m_out))
plot(m_out, type = "hist")
dev.off()
###卡钳匹配（Caliper Matching）,按照Austin的推荐设定卡钳值
caliper_width<-sd(log(new_seer$propensity_score))*0.2
m_out2<-matchit(formula1, data =new_seer, method = "nearest",
                distance="glm", m.order= "random",ratio=1,
                caliper=caliper_width,replace=FALSE)#glm可由new_seer$propensity_score代替
plot(summary(m_out2))
###全局最优匹配（Global Optimal Matching）
m_out3<-matchit(formula1, data =new_seer, method = "full",
                distance=new_seer$propensity_score, link="probit",caliper=0.1,replace=FALSE)
plot(summary(m_out3))
plot(m_out3, type = "hist")
#生成PSM后的数据
psm_matchit_data <- match.data(m_out)  

#计算匹配后的SMD比较均衡性
#cobalt检测平衡性
library(cobalt)
cobalt::bal.tab(m_out, ,thresholds=c(m=.1),un=TRUE)
cobalt::bal.plot(m_out,var.name="distance",
                 mirror=TRUE,type="histogram")

######备用，NaN（Not a Number）问题#####
# 检查匹配后的数据中的方差
#variances <- sapply(psm_matchit_data, function(x) {
#  if(is.numeric(x)) var(x, na.rm = TRUE) else NA
#})
# 查看方差为零的变量
#zero_variance_vars <- names(variances[variances == 0])
#print(zero_variance_vars)
# 排除方差为零的变量
#non_constant_vars <- names(variances[variances != 0])
#print(non_constant_vars)
# 设定一个方差阈值
#variance_threshold <- 1e-10
# 查看方差接近于零的变量
#near_zero_variance_vars <- names(variances[variances < variance_threshold])
#print(near_zero_variance_vars)
# 计算平衡性表
#library(cobalt)
#balance <- bal.tab(m_out, var.names = non_constant_vars)
# 打印平衡性表
#print(balance)

######匹配后的数据描述及均衡性比较--P值版######
tab2 <- CreateTableOne(vars = show_vars, data = psm_matchit_data, factorVars = show_vars,strata = strata_vars)
print(tab2)
save(psm_matchit_data,show_vars1,file="tongue_psm_data1005.Rdata")
