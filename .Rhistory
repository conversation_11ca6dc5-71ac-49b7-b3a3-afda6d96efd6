#转化为图学习器：
glrn = as_learner(graph)
#使用随机搜索进行超参数调优，采用 5 折交叉验证评估模型性能。
at = auto_tuner(
tuner = tnr("random_search"),
learner = glrn,
resampling = rsmp("cv", folds = 5),
measure = msr("classif.auc"),
term_evals = 10)
#在训练集上启动自动调参过程：
print(task$col_roles)
set.seed(123)
at$train(task, row_ids = split$train)
#查看最优超参数：
at$tuning_result
#可视化超参数maxdepth和minsplit的变化对模型性能的影响：
autoplot(at$tuning_instance, type = "points",
cols_x = c("classif.rpart.maxdepth", "classif.rpart.minsplit"))
#将最佳超参数应用于学习器，并在训练集上重新训练模型。
glrn$param_set$values = at$tuning_result$learner_param_vals[[1]]
glrn$train(task, row_ids = split$train)
#可视化学习器
model = glrn$base_learner()
autoplot(model, type = "ggparty")
model$importance()
str(model)
#模型预测及评估
pred = glrn$predict(task, row_ids = split$test)
pred
pred$confusion
pred$score(msr("classif.acc"))
pred$score(msr("classif.recall"))    # 召回率
pred$score(msr("classif.auc"))       # AUC
autoplot(pred, type = "roc")         # ROC曲线
library(precrec)
install.packages("precrec")
library(precrec)
autoplot(pred, type = "roc")         # ROC曲线
autoplot(pred, type = "prc")         # PR曲线
pred$confusion   # 混淆矩阵
autoplot(pred)
######结合多种机器学习模型预测结果######
#构建学习器
learner_logreg = lrn("classif.log_reg", predict_type = "prob",id="logreg")
learner_rf = lrn("classif.ranger", num.trees = 500,
predict_type = "prob",
mtry=to_tune(15,30),
min.node.size=to_tune(5,12),
max.depth=to_tune(3,9),
id="ranger")
learner_svm=lrn("classif.svm",predict_type="prob",
type="C-classification",
cost=to_tune(0.1,10),
gamma=to_tune(0.1,10),
kernel=to_tune(c("polynomial","radial","sigmoid")),
degree=to_tune(1,3),
id="svm")
learner_xgb=lrn("classif.xgboost",predict_type="response",
eta=to_tune(0,1),
gamma=to_tune(0,5),
max_depth=to_tune(1,8),
min_child_weight=to_tune(1,10),
subsample=to_tune(0.5,1),
colsample_bytree=to_tune(0.5,1),
nrounds=to_tune(20,30),
eval_metric=to_tune(c("merror","mlogloss")),
id="xgboost")
learners = list(learner_logreg,learner_rf,learner_svm,learner_xgb)
#使用PipeOp Branch从给定的图形中创建不同的路径分支，其中只有一支分支被评估
granp<-ppl("branch",learners)
granp$plot()
#转化为图学习器
glearner<-as_learner(granp)
glearner
#用auto_tuner()做嵌套重抽样调参
future::plan("multisession")
at<-auto_tuner(tuner = tnr("random_search"),
learner = glearner,
resampling = rsmp("cv",folds=3),
measure = msr("classif.ce"),
term_evals = 10)
rr<-resample(task,at,rsmp("cv",folds=4),store_models = TRUE)
rr$aggregate()
######随机森林分析######
#查看可用的学习器
lrns()
######随机森林分析######
#查看可用的学习器
lrns()
#使用随机森林算法
learners<-lrn('classif.ranger',num.trees=100,predict_type='prob',num.threads=5)
learners
#训练模型
learners$train(task=task,row_ids=split$train)
learners$model
#验证模型
prediction<-learners$predict(task=task,row_ids=split$test)
prediction
prediction$confusion
measure<-msr("classif.acc")
prediction$score(measure)#准确率
#绘制ROC曲线（2分类问题）
autoplot(prediction,type='roc')
######随机森林分析-4、重抽样######
#查看可用的重抽样方法
rsmps()
#创建任务
task<-TaskClassif$new(id = "threedata_task", backend = threedata,
target = "Three_year_status")
task
#定义重抽样方案
resampling<-rsmp('cv',folds=10)
resampling
#查看重抽样结果
cv10<-resampling$instantiate(task=task)
cv10$iters#10次
cv10$train_set(1)
cv10$test_set(1)
#使用重抽样进行模型评价
my_resample<-resample(task=task,learner=learners,resampling=cv10,store_models=TRUE)
my_resample
#模型评价
#汇总
my_resample$aggregate(measures=measure)  #0.93
#单次抽样
my_resample$score(measures=measure)$classif.acc
my_resample$score(measures=measure)$classif.acc%>%mean()
my_resample$resampling$train_set(1)
my_resample$resampling$test_set(1)
my_resample$prediction()
my_resample$learners[[1]]$model
learners=lrns(c("classif.rpart","classif.kknn","classif.ranger",
"classif.svm"),
predict_type="prob")
######基准测试-3、重抽样######
resampling<-rsmp('cv',folds=5)
resampling
#benchmark_grid
design=benchmark_grid(tasks,learners,resampling)
#benchmark_grid
design=benchmark_grid(task,learners,resampling)
#执行基准测试
bmr=benchmark(design)
######基准测试-4、基准测试######
print(task$feature_types())
######基准测试-4、基准测试######
print(task$feature_types)
learners=lrns(c("classif.rpart","classif.kknn","classif.ranger",
),
predict_type="prob")
learners=lrns(c("classif.rpart","classif.kknn","classif.ranger"
),
predict_type="prob")
######基准测试-3、重抽样######
resampling<-rsmp('cv',folds=5)
resampling
######基准测试-4、基准测试######
print(task$feature_types)
#benchmark_grid
design=benchmark_grid(task,learners,resampling)
#执行基准测试
bmr=benchmark(design)
######基准测试-5、效果评价######
measures=list(msr("classif.acc"),msr("classif.auc"))
bmr$aggregate(measures)  #汇总基准测试结果
autoplot(bmr,measure=msr("classif.auc"))  #可视化
#使用随机森林算法
learners<-lrn('classif.ranger',num.trees=100,predict_type='prob',num.threads=5)
learners
#训练模型
learners$train(task=task,row_ids=split$train)
learners$model
#验证模型
prediction<-learners$predict(task=task,row_ids=split$test)
prediction
prediction$confusion
######随机森林分析-3、模型评价######
#查看可用的模型评价指标
msrs()
measure<-msr("classif.acc")
prediction$score(measure)#准确率
#绘制ROC曲线（2分类问题）
autoplot(prediction,type='roc')
######随机森林分析-4、重抽样######
#查看可用的重抽样方法
rsmps()
#创建任务
task<-TaskClassif$new(id = "threedata_task", backend = threedata,
target = "Three_year_status")
task
#定义重抽样方案
resampling<-rsmp('cv',folds=10)
resampling
#查看重抽样结果
cv10<-resampling$instantiate(task=task)
cv10$iters#10次
cv10$train_set(1)
cv10$test_set(1)
s <- kernelshap(learners,X=threedata,bg_X=threedata)
??kernelshap
library(kernelshap)
install.packages("kernelshap")
library(kernelshap)
library(shapviz)
install.packages("shapviz")
library(shapviz)
s <- kernelshap(learners,X=threedata,bg_X=threedata)
prediction
rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_new_seer1005.Rdata")
str(new_seer)
colnames(new_seer)
# extract data for three year cohort
table(new_seer$Three_year_status_data)
threedata_full<-new_seer[new_seer$"Three_year_status_data" == 0,]
threedata_full<-as.data.frame(threedata_full[,c('Three_year_status',
"Age","Sex","Marital_status","RaceGroup","household_income_group",
"Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
"Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
"Surgery","Elective_Neck_Surgery","Surgery_Of_DX",
"Chemotherapy","Radiotherapy","Treatment")])
threedata <-na.omit(threedata_full)
threedata<-droplevels(threedata)
summary(threedata)#Three_year_status的值需为0和1，注意因子化后值变为1和2
str(threedata)
#threedata <- select(threedata,1,3:22)
#########导入包#################
#remotes::install_github("mlr-org/mlr3proba")
#remotes::install_github("mlr-org/mlr3learners")
#install_github("cran/pracma")
library(readxl)
library(table1)
library(survival)
library(survivalROC)
library(survminer)
library(rms)
library(CoxBoost)
library(magicfor)
library(randomForestSRC)
library(pec)
library(partykit)
library(party)
library(mlr3)
library(mlr3proba)
library(mlr3tuning)
library(magicfor)
library(mlr3learners)
library(mlr3extralearners)
library(mlr3verse)
library(paradox)
library(precrec)
seeds<-c(1:10)
skimr::skim(threedata)
#观察数据
library(dplyr)
glimpse(threedata) # 数据类别
dim(threedata) # 数据维数
#构建任务
threedata$Three_year_status <- as.factor(threedata$Three_year_status)
task<-TaskClassif$new(id = "threedata_task", backend = threedata,
target = "Three_year_status")
task
task$positive <- "1"#定义了“positive”类
#autoplot(task)
#cols = c("Sex", "Age")
#autoplot(task$clone()$select(cols), type = "pairs")
#划分训练集和测试集
set.seed(123)
split = partition(task,ratio = 0.8)  # 默认stratify = TRUE
#按 80% 和 20% 的比例划分为训练集和测试集，默认情况下会进行分层抽样
######特征工程，数据清洗######
task$missings()
#构建了一个处理缺失值的管道，包括指示缺失值的步骤和插补步骤
po_indicator = po("missind",
affect_columns = selector_type(c("numeric", "integer")),
type = "numeric")
impute_step = gunion(list(po_indicator, po("imputehist"))) %>>%
po("featureunion") %>>%
po("imputeoor")
impute_step$plot(horizontal = TRUE)
#处理分类特征，使用了合并因子、固定因子和样本插补的步骤
cat_step = po("collapsefactors",
param_vals = list(no_collapse_above_prevalence = 0.03)) %>>%
po("fixfactors") %>>%
po("imputesample")
cat_step$plot(horizontal = TRUE)
######选择学习器，并设置了一些超参数以便后续调优######
rpart_learner = lrn("classif.rpart", predict_type = "prob",  #每个类别的概率向量，即“后验概率”
keep_model = TRUE,
cp = to_tune(p_dbl(0, 0.1)),
minsplit = to_tune(p_int(10, 20)),
maxdepth = to_tune(p_int(2, 10)))
rpart_learner
#将插补步骤、分类特征处理步骤和学习器连接起来，形成一个完整的处理图
graph =  cat_step %>>% rpart_learner
graph$plot()
#转化为图学习器：
glrn = as_learner(graph)
#使用随机搜索进行超参数调优，采用 5 折交叉验证评估模型性能。
at = auto_tuner(
tuner = tnr("random_search"),
learner = glrn,
resampling = rsmp("cv", folds = 5),
measure = msr("classif.auc"),
term_evals = 10)
#在训练集上启动自动调参过程：
print(task$col_roles)
set.seed(123)
at$train(task, row_ids = split$train)
#查看最优超参数：
at$tuning_result
#可视化超参数maxdepth和minsplit的变化对模型性能的影响：
autoplot(at$tuning_instance, type = "points",
cols_x = c("classif.rpart.maxdepth", "classif.rpart.minsplit"))
#将最佳超参数应用于学习器，并在训练集上重新训练模型。
glrn$param_set$values = at$tuning_result$learner_param_vals[[1]]
glrn$train(task, row_ids = split$train)
library(kernelshap)
library(shapviz)
s <- kernelshap(glrn,X=threedata,bg_X=threedata)
#可视化学习器
model = glrn$base_learner()
s <- kernelshap(glrn,X=threedata,bg_X=threedata)
rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_new_seer1005.Rdata")
str(new_seer)
colnames(new_seer)
#                                                "household_income_group",
#                                                "Year_of_diagnosis",
#                                                "Primary_Site",
#                                                "Tumor_Size","Grade",
#                                                "Stage_T","Stage_N","Stage_M",
#                                                "Stage","Summary_stage",
#                                                "Surgery","Elective_Neck_Surgery",
#                                                "Surgery_Of_DX",
#                                                "Chemotherapy","Radiotherapy",
#                                                "Treatment")])
survdata_full<-as.data.frame(new_seer[,c('Vital_status',
'Survival.months',
"Age","Sex","Marital_status",
"RaceGroup",
"household_income_group",
"Year_of_diagnosis",
"Primary_Site",
"Tumor_Size","Grade",
"Stage_T","Stage_N","Stage_M",
"Stage","Summary_stage",
"Surgery","Elective_Neck_Surgery",
"Surgery_Of_DX",
"Chemotherapy","Radiotherapy",
"Treatment")])
survdata <-na.omit(survdata_full)
survdata <-na.omit(survdata_full)
survdata<-droplevels(survdata)
summary(survdata)#Three_year_status的值需为0和1，注意因子化后值变为1和2
str(survdata)
survdata$Vital_status <- ifelse(survdata$Vital_status ==
"Dead", 1, 0)
#########导入包##################
#remotes::install_github("mlr-org/mlr3learners")
#remotes::install_github("mlr-org/mlr3verse")
library(readxl)
library(table1)
library(survival)
library(survivalROC)
library(survminer)
library(rms)
library(CoxBoost)
library(magicfor)
library(randomForestSRC)
library(pec)
library(partykit)
library(party)
library(mlr3)
library(mlr3proba)
library(mlr3tuning)
library(magicfor)
library(mlr3learners)
library(mlr3extralearners)
library(mlr3verse)
library(paradox)
seeds<-c(1:10)
skimr::skim(survdata)
#观察数据
library(dplyr)
glimpse(survdata) # 数据类别
dim(survdata) # 数据维数
set.seed(19910220)
index1 <- sample(1:nrow(threedata), round(0.8*nrow(threedata)))
set.seed(19910220)
index1 <- sample(1:nrow(survdata), round(0.8*nrow(survdata)))
train_set_1 <- survdata[index1,]
test_set_1 <- survdata[-index1,]
cox1 <- coxph(Surv(survival_months, Vital_status) ~., x = T, y = T,
data = train_set_1)
cox1 <- coxph(Surv(Survival.months, Vital_status) ~., x = T, y = T,
data = train_set_1)
tree1 <- rpart(Surv(Survival.months, Vital_status)~.,
data=train_set_1)
rsf1 <- rfsrc(Surv(Survival.months, Vital_status)~.,
data=train_set_1,
ntree=100,forest=TRUE,
tree.err=T, importance=T,
na.action = "na.impute")
bst1 <- pecCforest(Surv(Survival.months, Vital_status)~.,
data=train_set_1)
cf1=calPlot(list(cox1 ,rsf1,tree1),
col=c("black",'green','red'),
time=36,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
str(survdata)
summary(survdata)
summary(train_set_1)
cf1=calPlot(list(cox1,rsf1,tree1),
col=c("black",'green','red'),
time=36,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
summary(test_set_1)
sum(is.na(test_set_1))
sum(is.na(train_set_1))
View(train_set_1)
cox1 <- coxph(Surv(Survival.months, Vital_status) ~.,
data = train_set_1)
tree1 <- rpart(Surv(Survival.months, Vital_status)~.,
data=train_set_1)
cf1=calPlot(list(cox1,rsf1,tree1),
col=c("black",'green','red'),
time=36,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
cox1 <- coxph(Surv(Survival.months, Vital_status) ~., x = T,
data = train_set_1)
cf1=calPlot(list(cox1,rsf1,tree1),
col=c("black",'green','red'),
time=36,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
cox1 <- coxph(Surv(Survival.months, Vital_status) ~., x = T, y = T,
data = train_set_1)
cf1=calPlot(list(cox1,rsf1,tree1),
col=c("black",'green','red'),
time=36,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
cox1 <- coxph(Surv(Survival.months, Vital_status) ~.,
data = train_set_1)
bst1 <- pecCforest(Surv(Survival.months, Vital_status)~.,
data=train_set_1)
cf1=calPlot(list(cox1,rsf1,tree1),
col=c("black",'green','red'),
time=36,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
cox1 <- coxph(Surv(Survival.months, Vital_status) ~., x = TRUE, y = TRUE,
data = train_set_1)
tree1 <- rpart(Surv(Survival.months, Vital_status)~.,
data=train_set_1)
rsf1 <- rfsrc(Surv(Survival.months, Vital_status)~.,
data=train_set_1,
ntree=100,forest=TRUE,
tree.err=T, importance=T,
na.action = "na.impute")
bst1 <- pecCforest(Surv(Survival.months, Vital_status)~.,
data=train_set_1)
cf1=calPlot(list(cox1,rsf1,tree1),
col=c("black",'green','red'),
time=12,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
bst1
cf1=calPlot(list(cox1,rsf1,tree1),
col=c("black",'green','red'),
time=36,
type="survival",
legend=F,
data = test_set_1,
splitMethod = "cv",
B=10
)
plot(bst1)
