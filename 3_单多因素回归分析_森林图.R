rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_psm_data1005.Rdata")
######所有变量：自变量、协变量、结果变量######
#vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
#        "Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup","Grade",
#        "Depth_Of_Invasion","Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
#        "Surgery","Surgery_Prim_Site","Elective_Neck_Surgery","Surgery_Of_DX",
#        "Chemotherapy","Radiotherapy","Adjuvant_Radiotherapy","Treatment","CSS",
#        "Survival.months","Vital_status","1_year_status","3_year_status",
#        "5_year_status","10_year_status","Recurrence_time","Recurrence_status") 

#formula1 <- Chemotherapy~Age+Sex+Marital_status+RaceGroup+household_income_group+
#  Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
#  Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
#  Surgery_Of_DX+Radiotherapy #协变量，去除生存/复发状态、生存/复发时间

######单因素分析Cox回归分析######
vars_Cox<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
            "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
            "Stage_T","Stage_N","Stage_M","Stage","Surgery",
            "Elective_Neck_Surgery","Surgery_Of_DX","Chemotherapy",
            "Radiotherapy","Survival.months","Vital_status") #自变量、协变量、结果变量
strata_vars <- c("Chemotherapy")#自变量
Cox_psm_data<-psm_matchit_data[vars_Cox]
###批量显示协变量table####
tables_list <- lapply(vars_Cox, function(var) {
  table(Cox_psm_data[[var]])
})
names(tables_list) <- vars_Cox
tables_list
###计算HR (95%CI)
library(survival)
table(Cox_psm_data$Vital_status)
Cox_psm_data$Vital_status <- ifelse(Cox_psm_data$Vital_status ==
                                      "Dead", 1, 0)
#Cox回归模型中生存状态（Vital_status）psm_matchit_data，表示事件状态。通常，0表示个体仍然存活或事件未发生，1表示事件（如死亡）已发生。

table(Cox_psm_data$Survival.months)
cox_model <- coxph(Surv(Survival.months,Vital_status) ~ Chemotherapy, data = Cox_psm_data)
cox_model$coefficients
pval=anova(cox_model)$Pr[2]
summary(cox_model)#exp(coef)为1.863，即HR=1.86；lower .95和upper .95分别为1.537和2.259，即HR的95%置信区间为（1.54，2.26）
#Schoenfeld残差检验，以评估协变量是否满足PH假定
library(survival)
cox.zph(cox_model)#对协变量Adjuvant_Radiotherapy进行残差检验得到的χ2=0.484（chisq为χ2），P值为0.49，大于0.05，可以认为满足PH假定。

library(gtsummary)
tm1 <- tbl_regression(cox_model, 
                      exponentiate = TRUE, 
                      include = c(Chemotherapy),
                      pvalue_fun = ~style_pvalue(.x, digits = 3))

tm1
tbl_summary(Cox_psm_data)
tbl_summary(Cox_psm_data, by = Chemotherapy)


######绘制K-M曲线######
library(survminer)
library(tidyverse)
table(Cox_psm_data$Chemotherapy)#确定变量水平顺序
fit_psm <- survfit(Surv(Survival.months,Vital_status) ~Chemotherapy,data = Cox_psm_data) # 数据集来源
levels(Cox_psm_data$Chemotherapy)
ggsurvplot(fit_psm, data =Cox_psm_data,
           conf.int = T,
           pval.coord=c(1,0.5),
           pval = TRUE, pval.method = F, 
           pval.size = 5,#p值参数（坐标/大小等）
           risk.table = TRUE,
           risk.table.height=0.2,
           title="Overall survival", #标题
           legend.labs = c("No", "Yes "),# 根据变量水平顺序设置图例标签
           legend.title="Chemotherapy", 
           font.legend = 14,#legend参数调整
           font.main = c(16, "bold", "darkblue"),font.tickslab = 12,#字体调整
           linetype = c('solid','solid'), # Change line type by groups
           ylab="Cumulative survival (percentage)",xlab = " Time (Months)", #更改横纵坐标
           censor.shape = 124,censor.size = 2, #删失点的形状和大小
           ylim=c(0,1),xlim=c(0,72),
           break.x.by = 12,#横坐标间隔
           xscale=1,
           fontsize=6,
           surv.median.line = "hv",
           ncensor.plot = F,
           ggtheme = theme_survminer())


######逐步回归法筛选变量######
summary(Cox_psm_data)
######1、自助法stepwise进行变量筛选######
# 列出所有自变量
variable_list <- names(Cox_psm_data)[!(names(Cox_psm_data) %in% c("Survival.months", "Vital_status"))]
# 构建公式
formula_str <- paste("Surv(Survival.months, Vital_status) ~", paste(variable_list, collapse = " + "))
# 使用 coxph 进行生存分析
fit.cox <- coxph(as.formula(formula_str), data = Cox_psm_data)
library(bootStepAIC)
#10次bootstrap
fit.boot <- boot.stepAIC(fit.cox,data=Cox_psm_data,direction="both",B=10,seed=123)
fit.boot
names(coef(fit.boot$OrigStepAIC))

######2、逐步选择法进行变量筛选######
fit.step <- step(fit.cox,direction = "both")
AIC(fit.cox)
AIC(fit.step)
BIC(fit.step)
step.coef <- coef(fit.step)
step.coef
step.lnc <- names(coef(fit.step))
step.lnc
broom::tidy(fit.step)
broom::glance(fit.step)

######批量单因素Cox回归生存分析######
#提取我们想要关注的特征
colnames(Cox_psm_data)
can <- c(colnames(Cox_psm_data)[1:18])
can
#分别对每一个变量，构建生存分析的公式
uni_sur<-sapply(can,function(x)as.formula(paste('Surv(Survival.months,Vital_status)~',x)))
uni_sur
#循环对每个特征做单因素cox回归分析
uni_cox<-lapply(uni_sur,function(x){coxph(x,data=Cox_psm_data)})
#提取风险比(HR),95%置信区间,P值（通过上面的介绍，大家可以找到哪里是我们需要的数据）
#创建一个列表uni_results，用于存储每个生存分析模型的结果
uni_results<-lapply(uni_cox,function(x){
  x<-summary(x)
  p.value<-signif(x$wald["pvalue"],digits=2)#获取Wald检验的p值并截断为两位小数
  wald.test<-signif(x$wald["test"],digits=2)
  beta<-signif(x$coef[1],digits=2);#coeficient beta
  HR<-signif(x$coef[2],digits=2) #获取模型系数的估计值（通常表示风险比HR）并截断为两位小数
  #获取HR的95%置信区间的下界和上界并截断为两位小数
  HR.confint.lower<-signif(x$conf.int[,"lower .95"],digits=2)
  HR.confint.upper<-signif(x$conf.int[,"upper .95"],digits=2)
  #构建HR和其95%置信区间的字符串表示
  HR <- paste0(HR, "(", HR.confint.lower, "-", HR.confint.upper, ")")
  #创建一个包含p值和HR（以及其95%置信区间）的结果向量
  res<-c(beta,wald.test,p.value,HR)
  #设置结果向量的名称，便于后续引用
  names(res) <- c("beta","wald.test", "p.value",rep("HR(95% CI for HR)", length(HR)))
  #返回结果向量
  return(res)
})
###将结果数据框化###
# 检查每个结果向量的长度
lengths <- sapply(uni_results, length)
print(lengths)

# 找到最大长度
max_length <- max(lengths)

# 确保所有结果向量的长度一致
uni_results_fixed <- lapply(uni_results, function(res) {
  if (length(res) < max_length) {
    # 用NA填充缺失值
    res <- c(res, rep(NA, max_length - length(res)))
  }
  return(res)
})

# 将结果转换为数据框以便于查看
uni_results_df <- do.call(rbind, uni_results_fixed)

# 检查输出
print(uni_results_df)

######多因素分析Cox回归分析######
levels(Cox_psm_data$Marital_status)
levels(Cox_psm_data$RaceGroup)
table(Cox_psm_data$RaceGroup)
table(Cox_psm_data$Tumor_Size)
#table(Cox_psm_data$Surgery_Prim_Site)
#levels(Cox_psm_data$Surgery_Prim_Site) <- list(
#  "Local_excision" = c("Local_excision"),
#  "Wide_excision" = c("Wide_excision"),
#  "Radical_excision" = c("Radical_excision")
#)
fit <- coxph(Surv(Survival.months,Vital_status) ~ .,
               data = Cox_psm_data)#这里可用“.”，代表分析数据中所有变量（列名）
fitSum=summary(fit)
outResult=data.frame()
outResult=cbind(
  HR=fitSum$conf.int[,"exp(coef)"],
  L95CI=fitSum$conf.int[,"lower .95"],
  H95CIH=fitSum$conf.int[,"upper .95"],
  pvalue=fitSum$coefficients[,"Pr(>|z|)"])
outResult=cbind(id=row.names(outResult),outResult)
######保存结果######
write.table(outResult,file="multiCox_tongue1007.txt",sep="\t",row.names=F,quote=F)
#同时显示单因素与多因素模型
library(autoReg)
fit2<-autoReg(fit,uni=TRUE,threshold=1, final=F)%>% myft() #threshold设定为1，纳入所有变量进入多因素分析，纳入最终的模型即final设为T
fit2
table2docx(fit2)#导出word格式#Exported table as Report.docx
table2pptx(fit2)#导出PPT格式#Exported table as Report.pptx

######绘制森林图######
ggforest(fit,data = Cox_psm_data)

######单因素和多因素cox回归森林图同时显示######
library(ggplot2)
library(ggthemes)
library(ggsci)
p2 <- modelPlot(fit,uni=TRUE,
                threshold=1,
                show.ref=FALSE,
                change.pointsize = T)

p2$p <- p2$p+
  scale_fill_nejm()+
  scale_color_nejm()

p2

######评价模型的预测能力:C-index######
#等于所有病人对子中预测结果与实际结果一致的对子所占的比例。
#通常认为，C-index在0.5-0.70之间为较低准确度，0.71-0.90之间为中等准确度，高于0.90为高准确度
sum.surv<- summary(fit) 
c_index <- sum.surv$concordance 
c_index

######评估生存分析模型性能:Time-ROC######
Cox_psm_data$three.years.death.probability<-
  c(1-(summary(survfit(fit,newdata = Cox_psm_data),times = 36)$surv))
Cox_psm_data$five.years.death.probability<-
  c(1-(summary(survfit(fit,newdata = Cox_psm_data),times = 60)$surv))
library(survivalROC)
SROC_3years<-survivalROC(Stime=Cox_psm_data$Survival.months,
                        status = Cox_psm_data$Vital_status,
                        marker = Cox_psm_data$three.years.death.probability,
                        predict.time = 36,
                        method = "KM")
summary(SROC_3years)
#计算最佳截止值
cut_3years<-SROC_3years$cut.values[which.max(SROC_3years$TP-SROC_3years$FP)]
cut_3years
SROC_3years$AUC #AUC值

#绘制ROC曲线
data<-data.frame(group=rep("3years-ROC",time=length(SROC_3years$FP)),
                 FP=SROC_3years$FP,
                 TP=SROC_3years$TP)
library(ggplot2) 
ggplot(data,aes(FP,TP))+
  geom_line(col="red")+
  labs(title = "3 years-ROC曲线",
       subtitle = paste0("AUC值:",round(SROC_3years$AUC,2)),
       xlim=c(0,1),ylim=c(0,1))+
  theme_bw()+
  geom_abline(intercept = 0, slope = 1, color = "blue")
#拟合5年ROC曲线
SROC_5years<-survivalROC(Stime=Cox_psm_data$Survival.months,
                         status = Cox_psm_data$Vital_status,
                         marker = Cox_psm_data$five.years.death.probability,
                         predict.time = 60,
                         method = "KM")
SROC_5years$AUC
#绘制5年和8年ROC曲线
data1<-data.frame(group=rep("5years-ROC",time=length(SROC_5years$FP)),
                  FP=SROC_5years$FP,
                  TP=SROC_5years$TP)
data<-rbind(data,data1)

ggplot(data,aes(FP,TP,col=group))+
  geom_line()+
  labs(title = "训练集3 years和5 years-ROC曲线",
       subtitle = paste0("3 years-AUC值:",
                         round(SROC_3years$AUC,2),
                         ";",
                         "5 years-AUC值:",
                         round(SROC_5years$AUC,2)),
       xlim=c(0,1),ylim=c(0,1))+
  theme_bw()+
  geom_abline(intercept = 0, slope = 1)

######衡量预测模型在不同时间点的区分度:时间依赖AUC（time-AUC）pec包法######
library(pec)
fcox<-cph(formula = Surv(Survival.months, Vital_status) ~ Chemotherapy+
            Age+Sex+Marital_status+RaceGroup+household_income_group+
            Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+Stage_T+
            Stage_N+Stage_M+Stage+Surgery+Elective_Neck_Surgery+
            Surgery_Of_DX+Radiotherapy, surv=T,x=T,y=T,data=Cox_psm_data)
C_index1<-cindex(list("model final"=fcox),#定义模型名称
                 eval.times = seq(10,120,10),#设置时间范围
                 cens.model = "cox",#指定回归方法为COX
                 keep.pvalues = T,  #保留p值
                 confint = T,       #可置信区间
                 confLevel = 0.95)   #置信区间范围

C_index1
#绘制time-AUC曲线
plot(C_index1,xlim = c(10,120),
     legend.x=3500,
     legend.y=0.95,
     legend.cex=0.8,
     col="red")
##使用Bootstrap（自助法）抽样500次验证结果，这样更具有代表性和稳定性
C_index2<-cindex(list("model final"=fcox),#定义模型名称
                 eval.times = seq(20,120,20),#设置时间范围
                 cens.model = "cox",#指定回归方法为COX
                 keep.pvalues = T,  #保留p值
                 confint = T,       #可置信区间
                 confLevel = 0.95,#置信区间范围
                 splitMethod = "bootcv", #采用Bootstrap交叉验证
                 B=50)    #500次抽样

plot(C_index2,xlim = c(10,120),
     legend.x=3500,
     legend.y=0.95,
     legend.cex=0.8,
     col="red")

###多模态time-AUC
fcox1<- cph(formula = Surv(Survival.months, Vital_status) ~ 
          Age+Sex+Marital_status+RaceGroup+household_income_group+
          Year_of_diagnosis+Grade+Stage_T+
          Stage_N+Stage+Surgery+Elective_Neck_Surgery+
          Radiotherapy, surv=T,x=T,y=T,data=Cox_psm_data) #重新构建不同自变量的模型

C_index3<-cindex(list("model final"=fcox,
                      "model new"=fcox1),#定义模型名称
                 eval.times = seq(20,120,20),#设置时间范围
                 cens.model = "cox",#指定回归方法为COX
                 keep.pvalues = T,  #保留p值
                 confint = T,       #可置信区间
                 confLevel = 0.95,#置信区间范围
                 splitMethod = "bootcv", #采用Bootstrap交叉验证
                 B=200)    #200次抽样

#绘制time-AUC曲线
plot(C_index3,
     xlim = c(20,120),
     legend.x=3500,
     legend.y=0.95,
     legend.cex=0.8,
     col=c("red","blue"))
legend("topright",            # 图例位置
       legend = c("model final", "model new"), # 图例文本
       col = c("red", "blue"), # 图例颜色
       lty = 1,                # 线型
       cex = 0.8)              # 字体大小
######图像无法显示######
while (!is.null(dev.list())) {
  dev.off()
}
######衡量预测模型在不同时间点的区分度:时间依赖AUC（time-AUC）timeROC包法######
test$risk_score<-predict(fit,newdata = test)

library(timeROC)
ROC.bili.marginal<-timeROC(T=test$time,
                           delta=test$status,
                           marker=test$risk_score,#计算ROC的biomaker，默认是marker值越大，事件越可能发生；反之的话，前面加-号。
                           cause=1, #所关心的事件结局
                           weighting="cox",#COX模型
                           times=quantile(pbc$time,probs=seq(0.2,0.8,0.05)),#想计算的ROC曲线的时间节点
                           iid=F)

ROC.bili.marginal
#绘制ROC曲线
plot(ROC.bili.marginal,time=36)        
plot(ROC.bili.marginal,time=120,add=TRUE,col="blue") 
legend("bottomright",c("Y-5","Y-8"),col=c("red","blue"),lty=1,lwd=2)
#绘制Time-Dependent AUC 曲线
plotAUCcurve(ROC.bili.marginal)


######保存data######
save(Cox_psm_data,vars_Cox, file = "Cox_psm_data_tongue1007.Rdata")






######cox回归亚组分析######
#假如确定亚组为Adjuvant_Radiotherapy,研究因素Age,GradeGroup,Tumor_Size
str(Cox_psm_data)
#clean_data=psm_matchit_data[,c(1:7,9,10)]
table(Cox_psm_data$Surgery_Prim_Site)

t1=subset(Cox_psm_data,Cox_psm_data$Tumor_Size=='001-005')
cox1=coxph(Surv(Survival.months, Vital_status) ~ Treatment, data = t1)
summary(cox1)
exp(confint(cox1,level=0.90))###95CI

t2=subset(Cox_psm_data,Cox_psm_data$Tumor_Size=='006-010')
cox2=coxph(Surv(Survival.months, Vital_status) ~ Treatment, data = t2)
exp(confint(cox2,level=0.90))

t3=subset(Cox_psm_data,Cox_psm_data$Tumor_Size=='011-015')
cox3=coxph(Surv(Survival.months, Vital_status) ~ Treatment, data = t3)
exp(confint(cox3,level=0.90))

t4=subset(Cox_psm_data,Cox_psm_data$Tumor_Size=='016-020')
cox4=coxph(Surv(Survival.months, Vital_status) ~ Treatment, data = t4)
exp(confint(cox4,level=0.90))

cox5=coxph(Surv(Survival.months, Vital_status) ~ Tumor_Size, 
           data = Cox_psm_data)
pval=anova(cox5)$Pr[2]
##
####
table(Cox_psm_data$Grade,Cox_psm_data$Treatment)

s1=subset(Cox_psm_data,Cox_psm_data$Grade=='I-II')
cox1=coxph(Surv(Survival.months, Vital_status) ~ Treatment, data = s1)
exp(confint(cox1,level=0.90))

s2=subset(Cox_psm_data,Cox_psm_data$Grade=='III-IV')
cox2=coxph(Surv(Survival.months, Vital_status) ~ Treatment, data = s2)
exp(confint(cox2,level=0.90))

cox5=coxph(Surv(Survival.months, Vital_status) ~ Grade, data = Cox_psm_data)
pval=anova(cox5)$Pr[2]

######9.倾向得分匹配后多种Cox模型：分层分析、脆弱模型和最佳稳健法######
#9.1 分层分析
fit3 <- coxph(Surv(Survival.months, Vital_status) ~ Treatment+strata(Grade)+
                Age + Sex +Marital_status+ RaceGroup +household_income_group +
                Year_of_diagnosis+Primary_Site+Tumor_Size+Surgery_Prim_Site, 
              data = Cox_psm_data)
summary(fit3)
#脆弱模型
fit4<-coxph(Surv(Survival.months, Vital_status) ~ Treatment + frailty(Sex)+ 
              Age + Grade +Marital_status+ RaceGroup +household_income_group +
              Year_of_diagnosis+Primary_Site+Tumor_Size+Surgery_Prim_Site, 
            data = Cox_psm_data,method = "efron", 
            control = coxph.control(iter.max = 100))
summary(fit4)
#稳健模型
fit5<-coxph(Surv(Survival.months, Vital_status) ~ Treatment + Age + Sex +
              Marital_status+ RaceGroup +household_income_group +Year_of_diagnosis+
              Primary_Site+Tumor_Size+Surgery_Prim_Site, cluster = Grade,
            data=Cox_psm_data)
summary(fit5)
#10.竞争性风险模型
table(seer$`SEER.cause-specific.death.classification`)
table(seer$SEER.other.cause.of.death.classification)
