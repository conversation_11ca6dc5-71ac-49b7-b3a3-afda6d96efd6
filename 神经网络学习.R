rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
library(readxl)

# 赋值文件名
file_path <- "tongue0922.xlsx"  # 请将此处替换为你的Excel文件路径
sheet_name <- "Sheet1"  # 请将此处替换为你的工作表名称

# 读取数据
data <- read_excel(file_path, sheet = sheet_name)
#data=read.table('SEEROS.txt',sep = '\t',header = T)

######列名处理，去除符号，..######
seer=data
seer <- as.data.frame(seer)
str(seer,list.len = Inf)
table(seer$`CS site-specific factor 1 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 2 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 3 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 4 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 5 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 6 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 8 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 9 (2004-2017 varying by schema)`)
table(seer$`CS site-specific factor 10 (2004-2017 varying by schema)`)

colnames(seer) <- gsub("[ ,]", ".", colnames(seer))
colnames(seer) <- gsub("\\.\\.+", ".", colnames(seer))

######筛选标准######
table(seer$Type.of.Reporting.Source)
seer=subset(seer,seer$Type.of.Reporting.Source !='Death certificate only')
seer=subset(seer,seer$Type.of.Reporting.Source !='Autopsy only')
table(seer$Diagnostic.Confirmation)
seer=subset(seer,seer$Diagnostic.Confirmation =='Positive histology' )
table(seer$Survival.months.flag)
seer=subset(seer,seer$Survival.months.flag =='Complete dates are available and there are 0 days of survival'|seer$Survival.months.flag =='Complete dates are available and there are more than 0 days of survival' )

table(seer$`Site.recode.ICD-O-3.2023.Revision`)
table(seer$`Site.recode.ICD-O-3/WHO.2008.(for.SIRs)`)
table(seer$`Site.recode.ICD-O-3.2023.Revision.Expanded`)
table(seer$`CS.Schema.-.AJCC.6th.Edition`)
table(seer$`Primary.Site.-.labeled`)
table(seer$`TNM.7/CS.v0204+.Schema.recode`)
table(seer$`TNM.7/CS.v0204+.Schema.(thru.2017)`)

table(seer$`ICD-O-3.Hist/behav.malignant`)
seer=subset(seer,seer$'ICD-O-3.Hist/behav.malignant' %in%
              c('8070/3: Squamous cell carcinoma, NOS',
                '8071/3: Squamous cell carcinoma, keratinizing, NOS',
                '8072/3: Squamous cell carcinoma, large cell, nonkeratinizing, NOS',
                '8073/3: Squamous cell carcinoma, small cell, nonkeratinizing',
                '8074/3: Squamous cell carcinoma, spindle cell',
                '8075/3: Squamous cell carcinoma, adenoid',
                '8076/3: Squamous cell carcinoma, micro-invasive',
                '8077/3: Squamous cell carcinoma, grade III',
                '8078/3: Squamous cell carcinoma with horn formation',
                '8083/3: Basaloid squamous cell carcinoma',
                '8084/3: Squamous cell carcinoma, clear cell type',
                '8085/3: Squamous cell carcinoma, HPV-positive',
                '8086/3: Squamous cell carcinoma, HPV-negative'))
table(seer$"Histology.recode.-.broad.groupings")

table(seer$First.malignant.primary.indicator)
table(seer$`Site.recode.ICD-O-3.2023.Revision`)
seer_recur=subset(seer,seer$`Site.recode.ICD-O-3.2023.Revision` %in% c('Tongue Anterior',
                                                                       "Buccal Mucosa","Floor Of Mouth","Gum","Mouth Other",
                                                                       "Oropharynx","Pharynx And Oral Cavity Other"))
table(seer_recur$"Record.number.recode")
seer_recur=subset(seer_recur,seer_recur$`Record.number.recode` %in% c('2'))
table(seer_recur$Primary.by.international.rules)
table(seer_recur$Sequence.number)
seer_recur=subset(seer_recur,seer_recur$`Sequence.number` %in% 
                    c('2nd of 2 or more primaries'))
table(seer_recur$First.malignant.primary.indicator)
seer_recur=subset(seer_recur,seer_recur$First.malignant.primary.indicator =="No")
table(seer_recur$Patient.ID)

seer=subset(seer,seer$`Site.recode.ICD-O-3.2023.Revision` =='Tongue Anterior')
seer=subset(seer,seer$First.malignant.primary.indicator =="Yes")
table(seer$Sequence.number)

######设置复发状态，复发时间######
library(dplyr)
check_duplicates <- function(patient_ID) {
  if (any(duplicated(patient_ID))) {
    print("Patient.ID 列中存在重复值")
  } else {
    print("Patient.ID 列中不存在重复值")
  }
}
# 检查重复值
check_duplicates(seer_recur$Patient.ID)
check_duplicates(seer$Patient.ID)
# 获取共同的Patient.ID
patient_ID_prim_recur <- intersect(seer_recur$Patient.ID, seer$Patient.ID)

# 过滤数据
seer_recur_filtered <- seer_recur %>%
  filter(Patient.ID %in% patient_ID_prim_recur)
table(seer_recur_filtered$`Site.recode.ICD-O-3.2023.Revision`)

seer_prim <- seer %>%
  filter(Patient.ID %in% patient_ID_prim_recur)

# 统计ICD-O-3.Hist/behav.malignant的频数
table(seer_recur_filtered$`ICD-O-3.Hist/behav.malignant`)
table(seer_prim$`ICD-O-3.Hist/behav.malignant`)

# 合并数据并计算Recurrence_time
merged_seer <- merge(seer_prim, 
                     seer_recur_filtered %>% select(Patient.ID, 
                                                    `ICD-O-3.Hist/behav.malignant`, Year.of.diagnosis), 
                     by = "Patient.ID")

matched_rows <- merged_seer %>%
  filter(`ICD-O-3.Hist/behav.malignant.x` == `ICD-O-3.Hist/behav.malignant.y`) %>%
  mutate(Recurrence_time = Year.of.diagnosis.y - Year.of.diagnosis.x) %>%
  select(Patient.ID, Recurrence_time)

# 合并回原始数据
seer <- merge(seer, matched_rows, by = "Patient.ID", all.x = TRUE)

# 设置Recurrence_status
seer <- seer %>%
  mutate(Recurrence_status = ifelse(Patient.ID %in% patient_ID_prim_recur, 1, 0))

# 更新Recurrence_time
seer <- seer %>%
  mutate(Recurrence_time = ifelse(!Patient.ID %in% patient_ID_prim_recur, 
                                  Survival.months, 
                                  Recurrence_time))

table(seer$Recurrence_time)
str(seer)

######年龄数据重新分组Age ######
names(seer)[names(seer) == "Age.Standard.for.Survival.(15-44.45-54.55-64.65-74.75+)"] <- "Age_group"
table(seer$Age_group)
library(dplyr)
seer <- seer %>%
  mutate(Age = case_when(
    Age_group %in% c("10-14 years", "15-19 years", "20-24 years", "25-29 years",
                     "30-34 years", "35-39 years", "40-44 years", "45-49 years") ~ "<50",
    Age_group %in% c("50-54 years", "55-59 years") ~ "50-59",
    Age_group %in% c("60-64 years", "65-69 years") ~ "60-69",
    Age_group %in% c("70-74 years", "75-79 years") ~ "70-79",
    Age_group %in% c("80-84 years", "85+ years") ~ "≥80",
    TRUE ~ NA_character_  # 处理可能的其他值
  ))
table(seer$Age)
summary(is.na(seer$Age))

######婚姻状态重新分组Marital_status######
table(seer$Marital.status.at.diagnosis)
for (i in 1:nrow(seer)) {
  if (seer$`Marital.status.at.diagnosis`[i] %in% c("Married (including common law)")) {
    seer$`Marital_status`[i] <- "Married"
  } else if (seer$`Marital.status.at.diagnosis`[i] %in% c("Unknown")) {
    seer$`Marital_status`[i] <- "Unknown"
  } else if(seer$`Marital.status.at.diagnosis`[i] %in% c("Divorced","Separated",
                                                         "Single (never married)","Unmarried or Domestic Partner","Widowed")) {
    seer$`Marital_status`[i] <- "Unmarried"
  }
}
table(seer$Marital_status)


######种族分类变量重新分组RaceGroup ######
table(seer$'Race.recode.(W.B.AI.API)')# 使用反引号引用包含特殊字符的列名
for(i in 1:nrow(seer)){
  if(seer$'Race.recode.(W.B.AI.API)'[i]=='White'){
    seer$RaceGroup[i]='White'
  }else if(seer$'Race.recode.(W.B.AI.API)'[i]=='Black'){
    seer$RaceGroup[i]='Black'
  }else if(seer$'Race.recode.(W.B.AI.API)'[i]=='Asian or Pacific Islander'){
    seer$RaceGroup[i]='Asian_Pacific Islander'
  }else if(seer$'Race.recode.(W.B.AI.API)'[i]=='Unknown'){
    seer$RaceGroup[i]='Unknown'
  }else{
    seer$RaceGroup[i]='Others'
  }
}
table(seer$RaceGroup)

library(dplyr)
seer <- seer %>%
  mutate(RaceGroup = factor(case_when(
    RaceGroup == "White" ~ "White",
    RaceGroup %in% c("Black", "Asian_Pacific Islander", "Others", "Unknown") ~ "Others",
    TRUE ~ NA_character_  # 处理其他未列出的情况
  )))

summary(is.na(seer$RaceGroup))

######收入数据分类变量重新分组为分类变量household_income_group######
names(seer)[names(seer) == "Median.household.income.inflation.adj.to.2022"] <-
  "household_income"
table(seer$household_income)
seer$household_income_group <- character(nrow(seer))
# 根据收入范围进行分组
for (i in 1:nrow(seer)) {
  if (seer$household_income[i] == "< $40,000") {
    seer$household_income_group[i] <- "<40000"
  } else if (seer$household_income[i] %in% c("$40,000 - $44,999", "$45,000 - $49,999", 
                                             "$50,000 - $54,999", "$55,000 - $59,999", 
                                             "$60,000 - $64,999", "$65,000 - $69,999", 
                                             "$70,000 - $74,999", "$75,000 - $79,999")) {
    seer$household_income_group[i] <- "40000-79999"
  } else if (seer$household_income[i] %in% c("$100,000 - $109,999", "$80,000 - $84,999",
                                             "$85,000 - $89,999", "$90,000 - $94,999",
                                             "$95,000 - $99,999","$110,000 - $119,999")) {
    seer$household_income_group[i] <- "80000-119999"
  } else if (seer$household_income[i] %in% c("Unknown/missing/no match/Not 1990-2022")) {
    seer$household_income_group[i] <- "Unknown"
  } else {seer$household_income_group[i] <- "120000+"
  }
}

table(seer$household_income_group)
summary(is.na(seer$household_income_group))

######诊断年份重新分组为分类变量Year_of_diagnosis ######
table(seer$Year.of.diagnosis)
summary(is.na(seer$Year.of.diagnosis))
seer$"Year_of_diagnosis"<- NA
### 将数值转换和分类分开处理
seer <- seer %>%
  ### 处理无法转换的值
  mutate(
    Year.of.diagnosis = case_when(
      grepl("^[0-9]+$", Year.of.diagnosis) ~ as.numeric(Year.of.diagnosis),
      TRUE ~ NA_real_
    ),
    Year_of_diagnosis = case_when(
      Year_of_diagnosis == "Unknown" ~ "Unknown",
      !is.na(Year.of.diagnosis) ~ as.character(cut(
        Year.of.diagnosis,
        breaks = c(-Inf, 2009, 2015, Inf),
        labels = c("2004-2009", "2010-2015", "2016-2021"),
        right = TRUE
      )),
      TRUE ~ "Unknown"
    )
  )
table(seer$Year_of_diagnosis)
summary(is.na(seer$Year_of_diagnosis))

######原发灶位置字符值去除特殊符号字符，重命名并分组Primary_Site ######
names(seer)[names(seer) == "Primary.Site.-.labeled"] <- "Primary_Site"
table(seer$Primary_Site)
#去除字符中-.和空格
seer$Primary_Site <- gsub("^[^-]+-(.*)$", "\\1", seer$Primary_Site)
# 如果您只想保留直到 "tongue" 为止的部分，可以进一步使用 gsub
seer$Primary_Site <- gsub("tongue.*$", "tongue", seer$Primary_Site)
seer$Primary_Site <- gsub("Anterior 2/3 of tongue", "Anterior of tongue",
                          seer$Primary_Site)
seer$Primary_Site <- gsub(" ", "_", seer$Primary_Site)
seer$Primary_Site <- gsub(",", "", seer$Primary_Site)
table(seer$Primary_Site)

######肿瘤大小连续变量重新分组为分类变量Tumor_Size######
table(seer$`Tumor.Size.Over.Time.Recode.(1988+)`)
class(seer$'Tumor.Size.Over.Time.Recode.(1988+)')
seer$`Tumor.Size.Over.Time.Recode.(1988+)`[seer$`Tumor.Size.Over.Time.Recode.(1988+)`==
                                             "000 (no evidence of primary tumor)"]="000"
seer$`Tumor.Size.Over.Time.Recode.(1988+)`[seer$`Tumor.Size.Over.Time.Recode.(1988+)`==
                                             "990 (microscopic focus)"]="990"
seer$`Tumor.Size.Over.Time.Recode.(1988+)`[seer$`Tumor.Size.Over.Time.Recode.(1988+)`==
                                             "Unknown or size unreasonable (includes any tumor sizes 401-989)"]="999"
seer$`Tumor.Size.Over.Time.Recode.(1988+)` <- as.numeric(seer$`Tumor.Size.Over.Time.Recode.(1988+)`)#将数据类型由字符型转换为数值类型
index005=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=005 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>0 |seer$`Tumor.Size.Over.Time.Recode.(1988+)`=="990")
index010=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=010 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>005)
index015=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=015 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>010)
index020=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=020 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>015)
index020_100=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=100 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>020)
index200=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=200 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>100)
index300=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=300 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>200)
index400=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)`<=400 & seer$`Tumor.Size.Over.Time.Recode.(1988+)`>300)
index999=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)` == "999")
index000=which(seer$`Tumor.Size.Over.Time.Recode.(1988+)` == "0")

seer$Tumor_Size <- NA
seer$Tumor_Size[index005]='001-005'
seer$Tumor_Size[index010]='006-010'
seer$Tumor_Size[index015]='011-015'
seer$Tumor_Size[index020]='016-020'
seer$Tumor_Size[index020_100]='021-100'
#seer$Tumor_Size[index100]='0-100'
seer$Tumor_Size[index200]='101-200'
seer$Tumor_Size[index300]='201-300'
seer$Tumor_Size[index400]='301-400'
seer$Tumor_Size[index999]='Unknown'
seer$Tumor_Size[index000]='000'

table(seer$Tumor_Size)

######浸润深度重新分组Depth_Of_Invasion ######
table(seer$`EOD.Primary.Tumor.(2018+)`)
for (i in 1:nrow(seer)) {
  if (seer$`EOD.Primary.Tumor.(2018+)`[i] %in% c("100","300")) {
    seer$`Depth_Of_Invasion`[i] <- "≤5/unknown"
  } else if (seer$`EOD.Primary.Tumor.(2018+)`[i] %in% c("150","400")) {
    seer$`Depth_Of_Invasion`[i] <- ">5,≤10"
  } else if (seer$`EOD.Primary.Tumor.(2018+)`[i] %in% c("200","500")) {
    seer$`Depth_Of_Invasion`[i] <- ">10"
  } else if (seer$`EOD.Primary.Tumor.(2018+)`[i] %in% c("600","650","700")) {
    seer$`Depth_Of_Invasion`[i] <- "T4"
  } else if (seer$`EOD.Primary.Tumor.(2018+)`[i] %in% c("999","Blank(s)")) {
    seer$`Depth_Of_Invasion`[i] <- "unknown"
  }
}
table(seer$`Depth_Of_Invasion`)
summary(is.na(seer$`Depth_Of_Invasion`))

######TNM合并、重新分组Stage_T/Stage_N/Stage_M ######
table(seer$`Derived.AJCC.T.6th.ed.(2004-2015)`)
table(seer$`Derived.AJCC.N.6th.ed.(2004-2015)`)
table(seer$`Derived.AJCC.M.6th.ed.(2004-2015)`)
table(seer$`Derived.SEER.Combined.T.(2016-2017)`)
table(seer$`Derived.SEER.Combined.N.(2016-2017)`)
table(seer$`Derived.SEER.Combined.M.(2016-2017)`)
table(seer$`Derived.EOD.2018.T.(2018+)`)
table(seer$`Derived.EOD.2018.N.(2018+)`)
table(seer$`Derived.EOD.2018.M.(2018+)`)

seer$Stage_T <- NA
seer$Stage_N <- NA
seer$Stage_M <- NA
for(i in 1:nrow(seer)){
  if(seer$`Derived.AJCC.T.6th.ed.(2004-2015)`[i] !='Blank(s)'){
    seer$Stage_T[i] = seer$`Derived.AJCC.T.6th.ed.(2004-2015)`[i]
  }else if(seer$`Derived.SEER.Combined.T.(2016-2017)`[i] !='Blank(s)'){
    seer$Stage_T[i] = seer$`Derived.SEER.Combined.T.(2016-2017)`[i]
  }else if(seer$`Derived.EOD.2018.T.(2018+)`[i]!='Blank(s)'){
    seer$Stage_T[i] = seer$`Derived.EOD.2018.T.(2018+)`[i]
  }else{
    seer$Stage_T[i] ='Unknown'
  }
}

for(i in 1:nrow(seer)){
  if(seer$`Derived.AJCC.N.6th.ed.(2004-2015)`[i] !='Blank(s)'){
    seer$Stage_N[i] = seer$`Derived.AJCC.N.6th.ed.(2004-2015)`[i]
  }else if(seer$`Derived.SEER.Combined.N.(2016-2017)`[i] != 'Blank(s)'){
    seer$Stage_N[i] = seer$`Derived.SEER.Combined.N.(2016-2017)`[i]
  }else if(seer$`Derived.EOD.2018.N.(2018+)`[i]!='Blank(s)'){
    seer$Stage_N[i] = seer$`Derived.EOD.2018.N.(2018+)`[i]
  }else{
    seer$Stage_N[i] ='Unknown'
  }
}

# 使用 dplyr 包进行矢量化操作
library(dplyr)

seer <- seer %>%
  mutate(Stage_M = case_when(
    `Derived.AJCC.M.6th.ed.(2004-2015)` != 'Blank(s)' ~ `Derived.AJCC.M.6th.ed.(2004-2015)`,
    `Derived.SEER.Combined.M.(2016-2017)` != 'Blank(s)' ~ `Derived.SEER.Combined.M.(2016-2017)`,
    `Derived.EOD.2018.M.(2018+)` != 'Blank(s)' ~ `Derived.EOD.2018.M.(2018+)`,
    TRUE ~ 'Unknown'
  ))

######提取或更换变量值中顺序字符形成新的变量值######
seer$`Stage_T` =substr(seer$`Stage_T`,2,2)#去除分期中的c\p\T\a\b,赋值数字
seer$`Stage_N` =substr(seer$`Stage_N`,2,2)
seer$`Stage_M` =substr(seer$`Stage_M`,2,2)

seer$`Stage_T` =gsub("8|x|n", "Unknown", seer$`Stage_T`, ignore.case = TRUE)#将88/Tx/Nx/Mx归类为unknown
seer$`Stage_N` =gsub("8|x|n", "Unknown", seer$`Stage_N`, ignore.case = TRUE)
seer$`Stage_M` =gsub("8|x|n", "Unknown", seer$`Stage_M`, ignore.case = TRUE)

table(seer$Stage_T)
table(seer$Stage_N)
table(seer$Stage_M)

######分期合并、重新分组Stage######
table(seer$`Derived.AJCC.Stage.Group.6th.ed.(2004-2015)`)
table(seer$`7th.Edition.Stage.Group.Recode.(2016-2017)`)
table(seer$`Derived.EOD.2018.Stage.Group.(2018+)`)

seer$Stage <- NA
for(i in 1:nrow(seer)){
  if(seer$`Derived.AJCC.Stage.Group.6th.ed.(2004-2015)`[i] !='Blank(s)'){
    seer$Stage[i] = seer$`Derived.AJCC.Stage.Group.6th.ed.(2004-2015)`[i]
  }else if(seer$`7th.Edition.Stage.Group.Recode.(2016-2017)`[i] !='Blank(s)'){
    seer$Stage[i] = seer$`7th.Edition.Stage.Group.Recode.(2016-2017)`[i]
  }else if(seer$`Derived.EOD.2018.Stage.Group.(2018+)`[i]!='Blank(s)'){
    seer$Stage[i] = seer$`Derived.EOD.2018.Stage.Group.(2018+)`[i]
  }else{
    seer$Stage[i] ='99'
  }
}

seer$Stage=gsub('A','',seer$Stage)#去除分期中的A\B\C
seer$Stage=gsub('B','',seer$Stage)
seer$Stage=gsub('C','',seer$Stage)

seer$Stage[seer$Stage=="1"]="I" #赋值分期以数字
seer$Stage[seer$Stage=="2"]="II"
seer$Stage[seer$Stage=="3"]="III"
seer$Stage[seer$Stage == "4" | seer$Stage == "IVNOS"]="IV"
seer$Stage[seer$Stage == "88" | seer$Stage == "99"| seer$Stage == "UNK Stage"]="Unknown"

table(seer$Stage)

######合并多列stage并重新分组summary_stage######
table(seer$`SEER.historic.stage.A.(1973-2015)`)
table(seer$`Summary.stage.2000.(1998-2017)`)
table(seer$`SEER.Combined.Summary.Stage.2000.(2004-2017)`)
table(seer$`Combined.Summary.Stage.(2004+)`)
library(dplyr)
seer <- seer %>%
  mutate(Summary_stage = case_when(
    `Summary.stage.2000.(1998-2017)` != 'Blank(s)' ~ `Summary.stage.2000.(1998-2017)`,
    `Combined.Summary.Stage.(2004+)` != 'Blank(s)' ~ `Combined.Summary.Stage.(2004+)`,
    TRUE ~ 'Unknown'
  ))
table(seer$Summary_stage)

######合并多列病理级别，并重新分组GradeGroup ######
table(seer$`Grade.Recode.(thru.2017)`)
table(seer$`Grade.Pathological.(2018+)`)
table(seer$`Grade.Clinical.(2018+)`)
table(seer$`Derived.Summary.Grade.2018.(2018+)`)

for(i in 1:nrow(seer)){
  if(seer$`Grade.Recode.(thru.2017)`[i] =='Well differentiated; Grade I' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='Blank(s)'){
    seer$GradeGroup[i]='Grade I'
  }else if(seer$`Grade.Recode.(thru.2017)`[i] =='Moderately differentiated; Grade II' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='Blank(s)'){
    seer$GradeGroup[i]='Grade II'
  }else if(seer$`Grade.Recode.(thru.2017)`[i] =='Poorly differentiated; Grade III' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='Blank(s)'){
    seer$GradeGroup[i]='Grade III'
  }else if(seer$`Grade.Recode.(thru.2017)`[i] =='Undifferentiated; anaplastic; Grade IV' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='Blank(s)'){
    seer$GradeGroup[i]='Grade IV'
  }else if(seer$`Grade.Recode.(thru.2017)`[i] =='Unknown' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='1' ){
    seer$GradeGroup[i]='Grade I'
  }else if(seer$`Grade.Recode.(thru.2017)`[i] =='Unknown' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='2' ){
    seer$GradeGroup[i]='Grade II'
  }else if(seer$`Grade.Recode.(thru.2017)`[i] =='Unknown' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='3' ){
    seer$GradeGroup[i]='Grade III'
  }else if(seer$`Grade.Recode.(thru.2017)`[i] =='Unknown' && seer$`Derived.Summary.Grade.2018.(2018+)`[i] =='4' ){
    seer$GradeGroup[i]='Grade IV'
  }else{
    seer$GradeGroup[i]='Unknown'
  }
}
table(seer$GradeGroup)
summary(is.na(seer$`GradeGroup`))


######手术方式数值更换成字符标记并重新分组Surgery_Prim_Site/Surgery ######
table(seer$`RX.Summ--Surg.Prim.Site.(1998+)`)
k=seer$`RX.Summ--Surg.Prim.Site.(1998+)`
seer$`Surgery_Prim_Site` <-NA
for (i in 1:nrow(seer)){
  if (k[i]==0) {
    seer$`Surgery_Prim_Site`[i]='No_surgery'
  }else if(9<as.numeric(k[i]) & as.numeric(k[i])<20){
    seer$`Surgery_Prim_Site`[i]='Local_destruction'
  }else if(19<as.numeric(k[i]) & as.numeric(k[i])<30){
    seer$`Surgery_Prim_Site`[i]='Local_excision'
  }else if(29<as.numeric(k[i]) & as.numeric(k[i])<40){
    seer$`Surgery_Prim_Site`[i]='Wide_excision'
  }else if(39<as.numeric(k[i]) & as.numeric(k[i])<80){
    seer$`Surgery_Prim_Site`[i]='Radical_excision'
  }else{
    seer$`Surgery_Prim_Site`[i]='Others'
  }
}

table(seer$Surgery_Prim_Site)
summary(is.na(seer$Surgery_Prim_Site))
seer$Surgery <-NA
for (i in 1:nrow(seer)) {
  if (seer$`RX.Summ--Surg.Prim.Site.(1998+)`[i] %in% c("0")) {
    seer$`Surgery`[i] <- "No"
  } else if (seer$`RX.Summ--Surg.Prim.Site.(1998+)`[i] %in% c("99")) {
    seer$`Surgery`[i] <- "Unknown"
  } else {
    seer$`Surgery`[i] <- "Yes"
  }
}

table(seer$Surgery)
table(seer$`Reason.no.cancer-directed.surgery`)
for (i in 1:nrow(seer)) {
  if (seer$`Reason.no.cancer-directed.surgery`[i] %in% c("Not performed, patient
      died prior to recommended surgery","Not recommended","Not recommended, 
      contraindicated due to other cond; autopsy only (1973-2002)","Recommended 
      but not performed, patient refused","Recommended but not performed, 
      unknown reason", "Unknown; death certificate; or autopsy only (2003+)")) {
    seer$`Surgery_From_Reason`[i] <- "No"
  } else if (seer$`Reason.no.cancer-directed.surgery`[i] %in% c("Recommended, unknown if performed")) {
    seer$`Surgery_From_Reason`[i] <- "Unknown"
  } else if (seer$`Reason.no.cancer-directed.surgery`[i] %in% c("Surgery performed")){
    seer$`Surgery_From_Reason`[i] <- "Yes"
  }
}
table(seer$Surgery_From_Reason)
summary(is.na(seer$Surgery_From_Reason))

seer_sur<-NA
seer_sur=subset(seer,seer$Surgery == "Unknown")
table(seer_sur$Surgery_From_Reason)
for (i in 1:nrow(seer)) {
  if (seer$Surgery[i] == "Unknown"){
    seer$Surgery[i] = seer$Surgery_From_Reason[i]
  }
}
table(seer$Surgery)
summary(is.na(seer$Surgery))

######淋巴结清扫手术分组Elective_Neck_Surgery######
seer$Elective_Neck_Surgery <-NA
table(seer$`Scope.of.reg.lymph.nd.surg.(1998-2002)`)
for (i in 1:nrow(seer)) {
  if (seer$`Scope.of.reg.lymph.nd.surg.(1998-2002)`[i] %in% c("0")) {
    seer$`Elective_Neck_Surgery`[i] <- "No"
  } else if (seer$`Scope.of.reg.lymph.nd.surg.(1998-2002)`[i] %in% c("9")) {
    seer$`Elective_Neck_Surgery`[i] <- "Unknown"
  } else if(seer$`Scope.of.reg.lymph.nd.surg.(1998-2002)`[i] %in% c("1","2","3","4","5")) {
    seer$`Elective_Neck_Surgery`[i] <- "Yes"
  }
}
is.na(seer$Elective_Neck_Surgery)
summary(is.na(seer$Elective_Neck_Surgery))
table(seer$`RX.Summ--Scope.Reg.LN.Sur.(2003+)`)
for (i in 1:nrow(seer)) {
  if (seer$"RX.Summ--Scope.Reg.LN.Sur.(2003+)"[i] %in% c("Biopsy or aspiration of regional lymph node, NOS",
                                                         "Sentinel lymph node biopsy","None")) {
    seer$`Elective_Neck_Surgery`[i] <- "No"
  } else if (seer$"RX.Summ--Scope.Reg.LN.Sur.(2003+)"[i] %in% c("Unknown or not applicable")) {
    seer$`Elective_Neck_Surgery`[i] <- "Unknown"
  } else if(seer$"RX.Summ--Scope.Reg.LN.Sur.(2003+)"[i] %in% c("1 to 3 regional lymph nodes removed",
                                                               "4 or more regional lymph nodes removed","Number of regional lymph nodes removed unknown",
                                                               "Sentinel node biopsy and lym nd removed same/unstated time",
                                                               "Sentinel node biopsy and lym nd removed different times")){
    seer$`Elective_Neck_Surgery`[i] <- "Yes"
  }
}
table(seer$Elective_Neck_Surgery)
summary(is.na(seer$Elective_Neck_Surgery))

######远处转移手术分组Surgery_Of_DX######
seer$Surgery_Of_DX <-NA
table(seer$`Surgery.of.oth.reg/dis.sites.(1998-2002)`)
for (i in 1:nrow(seer)) {
  if (seer$`Surgery.of.oth.reg/dis.sites.(1998-2002)`[i] %in% c("0","2","3","4")) {
    seer$`Surgery_Of_DX`[i] <- "No"
  } else if (seer$`Surgery.of.oth.reg/dis.sites.(1998-2002)`[i] %in% c("9")) {
    seer$`Surgery_Of_DX`[i] <- "Unknown"
  } else if(seer$`Surgery.of.oth.reg/dis.sites.(1998-2002)`[i] %in% c("5","6")) {
    seer$`Surgery_Of_DX`[i] <- "Yes"
  }else if(seer$`Surgery.of.oth.reg/dis.sites.(1998-2002)`[i] %in% c("1")) {
    seer$`Surgery_Of_DX`[i] <- "Surgery_NOS"
  }
}

table(seer$`RX.Summ--Surg.Oth.Reg/Dis.(2003+)`)
for (i in 1:nrow(seer)) {
  if (seer$`RX.Summ--Surg.Oth.Reg/Dis.(2003+)`[i] %in% c("None; diagnosed at autopsy",
                                                         "Non-primary surgical procedure to other regional sites")) {
    seer$`Surgery_Of_DX`[i] <- "No"
  } else if (seer$`RX.Summ--Surg.Oth.Reg/Dis.(2003+)`[i] %in% c("Unknown; death certificate only")) {
    seer$`Surgery_Of_DX`[i] <- "Unknown"
  } else if(seer$`RX.Summ--Surg.Oth.Reg/Dis.(2003+)`[i] %in% c("Non-primary surgical procedure to distant lymph node(s)",
                                                               "Non-primary surgical procedure to distant site",
                                                               "Any combo of sur proc to oth rg, dis lym nd, and/or dis site")) {
    seer$`Surgery_Of_DX`[i] <- "Yes"
  }else if(seer$`RX.Summ--Surg.Oth.Reg/Dis.(2003+)`[i] %in% c("Non-primary surgical procedure performed")) {
    seer$`Surgery_Of_DX`[i] <- "Yes"
  }
}
table(seer$Surgery_Of_DX)
summary(is.na(seer$Surgery_Of_DX))

######化疗信息合并分组Chemotherapy######
table(seer$`Chemotherapy.recode.(yes.no/unk)`)
table(seer$`RX.Summ--Systemic/Sur.Seq.(2007+)`)
seer$`Chemotherapy` <- NA
for (i in 1:nrow(seer)) {
  if (seer$`Chemotherapy.recode.(yes.no/unk)`[i] %in% c("Yes")) {
    seer$`Chemotherapy`[i] <- "Yes"
  } else if (seer$`RX.Summ--Systemic/Sur.Seq.(2007+)`[i] %in% c("Systemic therapy before surgery",
                                                                "Systemic therapy both before and after surgery","Systemic therapy after surgery",
                                                                "Surgery both before and after systemic therapy","Sequence unknown")) {
    seer$`Chemotherapy`[i] <- "Yes"
  } else if(seer$`RX.Summ--Systemic/Sur.Seq.(2007+)`[i] %in% c("No systemic therapy and/or surgical procedures") &&
            seer$`RX.Summ--Surg.Prim.Site.(1998+)`[i] !="0") {
    seer$`Chemotherapy`[i] <- "No/Unknown"                 ###确保有手术
  }else {
    seer$`Chemotherapy`[i] <- "No/Unknown"
  }
}
table(seer$Chemotherapy)
summary(is.na(seer$Chemotherapy))

######放疗信息重新分组Radiotherapy######
table(seer$`RX.Summ--Surg/Rad.Seq`)
table(seer$Radiation.recode)
seer$`Radiotherapy` <- NA
for (i in 1:nrow(seer)) {
  if (seer$`Radiation.recode`[i] %in% c("Beam radiation","Combination of beam 
          with implants or isotopes","Radiation, NOS  method or source not 
          specified","Radioactive implants (includes brachytherapy) (1988+)",
                                        "Radioisotopes (1988+)")) {
    seer$`Radiotherapy`[i] <- "Yes"
  } else if (seer$`Radiation.recode`[i] %in% c("Refused (1988+)")) {
    seer$`Radiotherapy`[i] <- "No/Unknown"
  } else if(seer$`RX.Summ--Surg/Rad.Seq`[i] %in% c("Intraoperative rad with 
                other rad before/after surgery", "Intraoperative radiation",
                                                   "Radiation after surgery","Radiation before and after surgery",
                                                   "Radiation prior to surgery","Sequence unknown, but both were given",
                                                   "Surgery both before and after radiation") ) {
    seer$`Radiotherapy`[i] <- "Yes"       
  }else if(seer$`RX.Summ--Surg/Rad.Seq`[i] %in% c("No radiation and/or no surgery; unknown if surgery and/or radiation given") && 
           seer$`RX.Summ--Surg.Prim.Site.(1998+)`[i] !="0"){
    seer$`Radiotherapy`[i] <- "No/Unknown"
  }else {
    seer$`Radiotherapy`[i] <- "No/Unknown"
  }
}
table(seer$Radiotherapy)
summary(is.na(seer$Radiotherapy))

######放疗信息细分组Adjuvant_Radiotherapy######
table(seer$`RX.Summ--Surg/Rad.Seq`)
seer$Adjuvant_Radiotherapy <- NA
for(i in 1:nrow(seer)){
  if(seer$`RX.Summ--Surg/Rad.Seq`[i] == "Radiation after surgery"){
    seer$Adjuvant_Radiotherapy[i] = 'Yes'
  } else {
    seer$Adjuvant_Radiotherapy[i] = 'No/Unkown'
  }
}
table(seer$Adjuvant_Radiotherapy)
summary(is.na(seer$Adjuvant_Radiotherapy))

######重新亚分组Grade######
table(seer$GradeGroup)
seer$Grade <- NA
for(i in 1:nrow(seer)){
  if(seer$`GradeGroup`[i] == "Grade I" | seer$`GradeGroup`[i] == "Grade II" ){
    seer$Grade[i] = 'I-II'
  } else if (seer$`GradeGroup`[i] == "Grade III" | seer$`GradeGroup`[i] == "Grade IV" ){
    seer$Grade[i] = 'III-IV'
  } 
}
table(seer$Grade)
summary(is.na(seer$Grade))


######设定肿瘤特异死亡CSS######
table(seer$`SEER.cause-specific.death.classification`)
table(seer$SEER.other.cause.of.death.classification)
seer$CSS[seer$SEER.other.cause.of.death.classification =="Dead (attributable to causes other than this cancer dx)"]<-"2"
seer$CSS[seer$`SEER.cause-specific.death.classification` =="Dead (attributable to this cancer dx)"]<-"1"
seer$CSS[seer$`SEER.cause-specific.death.classification` =="Dead (missing/unknown COD)"]<-"9"
seer$CSS[seer$'Vital.status.recode.(study.cutoff.used)' == "Alive"] <-"0"
table(seer$CSS)
summary(is.na(seer$CSS))

######重命名Vital_status######
names(seer)[names(seer) == "Vital.status.recode.(study.cutoff.used)"] <- "Vital_status"

######备份seer0,复活点seer0######
seer0=seer

######处理方式分组treatment#####
seer=seer0
table(seer$Surgery)
table(seer$Radiotherapy)

seer$Treatment3 <- NA
for(i in 1:nrow(seer)){
  if(seer$Surgery[i] == 'No' && seer$Radiotherapy[i] == 'Yes'){
    seer$Treatment3[i] = 'RT'
  } else if(seer$Surgery[i] == 'No' && seer$Radiotherapy[i] == 'No/Unknown'){
    seer$Treatment3[i] = 'No_treatment'
  } else if(seer$Surgery[i] == 'Yes' && seer$Radiotherapy[i] == 'Yes'){
    seer$Treatment3[i] = 'Surgery_Plus_RT'
  } else if(seer$Surgery[i] == 'Yes' && seer$Radiotherapy[i] == 'No/Unknown'){
    seer$Treatment3[i] = 'Surgery_alone'
  } else if(seer$Surgery[i] == 'Unknown') {
    seer$Treatment3[i] = 'Surgery_unknown'
  }
}
table(seer$Treatment3)
summary(is.na(seer$Treatment3))

seer$Treatment1 <- NA
for(i in 1:nrow(seer)){
  if(seer$Surgery[i] == 'No' && seer$Radiotherapy[i] == 'Yes'){
    seer$Treatment1[i] = 'RT'
  } else if(seer$Surgery[i] == 'No' && seer$Radiotherapy[i] == 'No/Unknown'){
    seer$Treatment1[i] = 'No_treatment'
  } else if(seer$Surgery[i] == 'Yes' && seer$Elective_Neck_Surgery[i] =="Yes" 
            &&seer$Radiotherapy[i] == 'Yes'){
    seer$Treatment1[i] = 'Surgery_ENS_RT'
  } else if(seer$Surgery[i] == 'Yes' && seer$Elective_Neck_Surgery[i] =="Yes" 
            && seer$Radiotherapy[i] == 'No/Unknown'){
    seer$Treatment1[i] = 'Surgery_ENS'
  } else if(seer$Surgery[i] == 'Yes' && seer$Elective_Neck_Surgery[i] =="No" 
            && seer$Radiotherapy[i] == 'Yes'){
    seer$Treatment1[i] = 'Surgery_RT'
  } else if(seer$Surgery[i] == 'Yes' && seer$Elective_Neck_Surgery[i] =="No" 
            && seer$Radiotherapy[i] == 'No/Unknown'){
    seer$Treatment1[i] = 'Surgery_alone'
  } else if (seer$Surgery[i] == 'Unknown' | seer$Elective_Neck_Surgery[i] =="Unknown") {
    seer$Treatment1[i] ='Unknown'
  }
}
table(seer$Treatment1)
summary(is.na(seer$Treatment1))

table(seer$`Chemotherapy`)

table(seer$Radiotherapy)
table(seer$`RX.Summ--Surg/Rad.Seq`)
table(seer$Surgery)
table(seer$`Surgery_Prim_Site`)
table(seer$Elective_Neck_Surgery)

table(seer$Surgery_Of_DX)

######进一步筛选患者TNM,复活点seer1######
seer1=seer#复活点

seer=seer1

table((seer$Marital_status))
seer=subset(seer,seer$Marital_status != "Unknown")
summary(is.na(seer$Marital_status))
seer=subset(seer,!seer$`RaceGroup` =='Unknown')
seer=subset(seer,!seer$`household_income_group` =='Unknown')
seer=subset(seer,!seer$`Stage_T` =='Unknown')
seer=subset(seer,!seer$`Stage_T` =='i')
seer=subset(seer,!seer$`Stage_T` =='0')
seer=subset(seer,!seer$`Stage_N` =='Unknown')
seer=subset(seer,!seer$`Stage_M` =='Unknown')
seer=subset(seer,!seer$`Stage` =='Unknown')
seer=subset(seer,!seer$`Summary_stage` =='Unknown/unstaged')
seer=subset(seer,!seer$`GradeGroup` =='Unknown')
seer=subset(seer,!seer$`Surgery` =='Unknown')
seer=subset(seer,!seer$`Elective_Neck_Surgery` =='Unknown')
seer=subset(seer,!seer$`Surgery_Of_DX` =='Unknown')
seer=subset(seer,!seer$CSS %in% c("9"))
seer=subset(seer,(seer$`Chemotherapy` %in% c("No/Unknown")))
seer <- subset(seer,(seer$Surgery_Of_DX %in% c("No")))
seer <- subset(seer, seer$Stage_T =="1" )
seer <- subset(seer, seer$Stage_N =="0" )
seer <- subset(seer, seer$Stage_M =="0" )
table(seer$Stage)
stage_iv_rows <- seer[seer$Stage == "IV", ]
print(stage_iv_rows)
table(seer$Summary_stage)
seer <- subset(seer, 
               !(seer$Summary_stage %in% c("Distant")))
table(seer$Tumor_Size)
seer <- subset(seer, 
               !(seer$Tumor_Size %in% c("021-100","Unknown")))
table(seer$Survival.months)
seer<-subset(seer,seer$Survival.months>30 )
seer=subset(seer,seer$Surgery=="Yes")

table(seer$COD.to.site.recode)
table(seer$COD.to.site.rec.KM)
table(seer$`EOD.Regional.Nodes.(2018+)`)
table(seer$`EOD.Mets.(2018+)`)
table(seer$`Regional.nodes.examined.(1988+)`)
table(seer$`Regional.nodes.positive.(1988+)`)
table(seer$`CS.extension.(2004-2015)`)
table(seer$`CS.lymph.nodes.(2004-2015)`)
table(seer$`CS.mets.at.dx.(2004-2015)`)
table(seer$`CS.Tumor.Size/Ext.Eval.(2004-2015)`)
table(seer$`CS.Reg.Node.Eval.(2004-2015)`)
table(seer$`CS.Mets.Eval.(2004-2015)`)
table(seer$`RX.Summ--Scope.Reg.LN.Sur.(2003+)`)
table(seer$`Scope.of.reg.lymph.nd.surg.(1998-2002)`)
table(seer$`RX.Summ--Reg.LN.Examined.(1998-2002)`)
table(seer$`Surgery.of.oth.reg/dis.sites.(1998-2002)`)
table(seer$`RX.Summ--Surg.Oth.Reg/Dis.(2003+)`)
table(seer$`SEER.Combined.Mets.at.DX-bone.(2010+)`)
table(seer$`SEER.Combined.Mets.at.DX-brain.(2010+)`)
table(seer$`SEER.Combined.Mets.at.DX-liver.(2010+)`)
table(seer$`SEER.Combined.Mets.at.DX-lung.(2010+)`)
table(seer$`Mets.at.DX-Distant.LN.(2016+)`)
table(seer$`Mets.at.DX-Other.(2016+)`)
str(seer)

######筛选变量，确定新的研究数据，"Survival.months","Vital_status"放后面######
table(seer$Surgery)
table(seer$Surgery_Prim_Site)
table(seer$Elective_Neck_Surgery)
table(seer$Radiotherapy)
table(seer$Adjuvant_Radiotherapy)
table(seer$Treatment1)
######Treatment1换成其他处理组,后续Treatment通用不变######
seer$Treatment=seer$Treatment1#Treatment1换成其他处理组
table(seer$Treatment)
seer=subset(seer,seer$Treatment %in% c("Surgery_ENS","Surgery_RT"))
str(seer,list.len = Inf)
######如还需筛选病例，最后1次机会######
######确定最终病例及设置变量######
vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
        "Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup","Grade",
        "Surgery","Surgery_Prim_Site","Elective_Neck_Surgery","Radiotherapy",
        "Adjuvant_Radiotherapy","Treatment","CSS","Recurrence_status",
        "Survival.months","Vital_status") 
vars <- vars[vars %in% colnames(seer)]
new_seer <- seer[vars]
str(new_seer)
vars_category<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
                 "Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup",
                 "Grade","Surgery","Surgery_Prim_Site","Elective_Neck_Surgery",
                 "Radiotherapy","Adjuvant_Radiotherapy","Treatment","Recurrence_status",
                 "CSS","Vital_status") 
vars_category <- vars_category[vars_category %in% colnames(new_seer)]
new_seer[vars_category] <- lapply(new_seer[vars_category], factor)#批量因子化
str(new_seer)
new_seer$Survival.months <-as.numeric(new_seer$Survival.months)
######批量查看因子顺序######
lever <- function(x) {
  return(levels(x))
}
factor_levels <- lapply(new_seer[vars_category], lever)# 使用 lapply 对 new_vars 中的每个因子变量应用 lever 函数
print(factor_levels)
######调整因子顺序######
new_seer$Age <- factor(new_seer$Age, levels = c("<50","50-59", "60-69", "70-79","≥80"))
levels(new_seer$household_income_group)= c("<40000","40000-79999", 
                                           "80000-119999", "120000+")
table(new_seer$RaceGroup)
levels(new_seer$RaceGroup)= c("White","Others")
levels(new_seer$Marital_status )= c("Married","Unmarried","Unknown")
str(new_seer)
summary(is.na(new_seer$RaceGroup))

###批量显示协变量table####
tables_list <- lapply(vars, function(var) {
  table(new_seer[[var]])
})
names(tables_list) <- vars
tables_list
######基线特征及差异性分析######
show_vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
             "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
             "Surgery_Prim_Site","Elective_Neck_Surgery") 
strata_vars <- c("Treatment")
library(tableone)
library(MatchIt)
tab1 <- CreateTableOne(vars = show_vars, data = new_seer, factorVars = show_vars,
                       strata = strata_vars,smd = TRUE)
print(tab1, smd = TRUE, showAllLevels = TRUE)
tabMat<-print(tab1,showAllLevels=TRUE,noSpaces=TRUE,printToggle=FALSE)
write.csv(tabMat,file="tabMat_TableOne.csv")
library(officer)
doc <- read_docx()
tabMat_df <- as.data.frame(tabMat)
doc <- body_add_table(doc, value = tabMat_df, style = "table_template")
print(doc, target = "tabMat_TableOne.docx")

library(xtable)
library(flextable)
library(officer)
tabMat_flextable<-as_flextable(xtable(tabMat))
doc_flextable=read_docx()
doc_flextable =body_add_flextable(doc_flextable,tabMat_flextable)
print(doc_flextable,"tabMat_flextable.docx")

######moonbook包制作临床三线图######
show_vars1<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
              "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
              "Surgery_Prim_Site","Elective_Neck_Surgery","Treatment") 
show_new_seer = new_seer[show_vars1]
library(moonBook)
table_moonbook <- mytable(Treatment~.,data=show_new_seer,digits=2,method=1,catMethod=0, show.total=TRUE)
table_moonbook <-mytable(Grade+Treatment~.,data=show_new_seer,digits=2,method=1,catMethod=0)
mycsv(table_moonbook,file="baseline_moonbook.csv")
library(autoReg)
library(dplyr)
ft <- gaze(Treatment~.,data=show_new_seer,digits=2,method=1,catMethod=0,show.p=TRUE) %>% 
  myft()
print(ft)
library(rrtable)
table2pptx(ft)
table2docx(ft)
######table1包制作临床三线图######
library(table1)
table1(~Age+Sex+Marital_status+RaceGroup+household_income_group+
         Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+
         Surgery_Prim_Site+Elective_Neck_Surgery|Treatment,
       data=new_seer,topclass="Rtable1-zebra")

chisq.test(new_seer$Age,new_seer$Treatment)
## 加入p值
pvalue <- function(x, ...) { 
  y <- unlist(x)  
  g <- factor(rep(1:length(x), times=sapply(x, length)))
  if (is.numeric(y)) {    
    p <- t.test(y ~ g)$p.value #数值型数据用t-test(两组比较)  
  } else {    
    tbl <- table(y, g)
    if (any(tbl < 5)) {
      p <- fisher.test(tbl)$p.value # 使用 Fisher 检验
    } 
    else {    
      p <- chisq.test(table(y, g))$p.value #因子型数据用卡方  
    }  
  }
  c("", sub("<", "&lt;", format.pval(p, digits=3, eps=0.001)))
}

table1(~Age+Sex+Marital_status+RaceGroup+household_income_group+
         Year_of_diagnosis+Primary_Site+Tumor_Size+Grade+
         Surgery_Prim_Site|Treatment,data=new_seer,
       extra.col=list(`P-value`=pvalue), overall=F,topclass="Rtable1-zebra")
dev.off()
######gtsummary包制作三线图######
library(gtsummary)
theme_gtsummary_journal(journal="jama")#使用JAMA主题
theme_gtsummary_compact()
tbl_summary(show_new_seer)
tbl_summary(show_new_seer, by = Treatment)
library(stringr)  # 确保加载 stringr 包
show_new_seer %>%tbl_strata(
  strata = Grade,
  label = list(RaceGroup = "Race",
               household_income_group = "Household_income"),
  ~.x %>% 
    tbl_summary(by = Treatment)%>%
    add_overall()%>%   # 添加 add_overall 函数以确保正确显示标签
    add_stat_label()  
) 

t1 <-show_new_seer %>%
  tbl_summary(by = Treatment,
              label=list(Age~"Patient Age",
                         RaceGroup = "Race"),
              digits=list(all_continuous()~ 2,
                          all_categorical()~2)) %>%
  add_p(pvalue_fun =~style_pvalue(.x,digits=3)) %>%
  add_overall()%>%
  add_stat_label()
t1%>%
  as_hux_xlsx(file='table_1.xlsx')
t1 %>%
  as_flex_table()%>%
  flextable::save_as_docx(path='table_1.docx')
reset_gtsummary_theme()#清除主题

table(new_seer$Adjuvant_Radiotherapy)
######根据treatment筛选数据，复活点new_seer2######
new_seer2=new_seer
new_seer=new_seer2
table(new_seer$Treatment)
summary(is.na(new_seer$Treatment))
summary(new_seer)
ML_vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
        "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
        "Surgery_Prim_Site","Radiotherapy","Treatment","Recurrence_status") 
new_seer <- new_seer[ML_vars]
new_seer=subset(new_seer,new_seer$Surgery_Prim_Site!="Others")
new_seer <-na.omit(new_seer)
new_seer<-droplevels(new_seer)
summary(new_seer)
######神经网络学习:分类变量必须转换成数值变量######
library(keras)
library(tensorflow)
#install.packages("tensorflow")
######热编码######
library(caret)
dataS=new_seer[,c(1:13)]
dumy_data=dummyVars("~.",data=dataS)
data_forDeepNet= data.frame(predict(dumy_data,newdata=dataS))
data_forDeepNet=as.matrix(data_forDeepNet)
######测试集和验证集######
set.seed(123)
index <- sample(nrow(data_forDeepNet),size = round(nrow(data_forDeepNet)*0.7))
train_x=data_forDeepNet[index,1:37]
#unique(new_seer$Recurrence_status)
library(keras)
table(new_seer$Recurrence_status)
train_y=data_forDeepNet[index,38:39]
#train_y = to_categorical(new_seer$Recurrence_status[index], 2)
test_x=data_forDeepNet[-index,1:37]
test_y=data_forDeepNet[-index,38:39]
#test_y=to_categorical(new_seer$Recurrence_status[-index],num_classes = 2)
######搭建神经网络######
seer_Deepnet <- keras_model_sequential() %>%
  layer_dense(units = 64, activation = "relu", input_shape=66, name = "den1") %>%
  layer_dropout(rate = 0.25) %>%
  layer_dense(units = 32, activation = "relu", name = "den3") %>%
  layer_gaussian_dropout(rate = 0.25) %>%
  layer_dense(units = 2, activation = "softmax", name = "output_layer")
summary(seer_Deepnet)

simple_model <- keras_model_sequential() %>%
  layer_dense(units = 2, activation = "softmax", input_shape = c(66),name = "output_layer")

######设置loss函数，优化器######
opt=optimizer_adam(lr = 0.001, decay=0)
opt=optimizer_sgd()
seer_Deepnet%>% compile(
  optimizer = opt,
  loss = "categorical_crossentropy",
  metrics= c("accuracy")
)
model=seer_Deepnet
summary(model)

A <- data.frame(
  Recurrence_status = c(0, 1, 0, 0, 1, 0, 1, 0, 0, 1)
)

# 示例索引
trainindex <- c(1, 2, 3, 5, 7, 10)

# 检查数据的类别分布
table(A$Recurrence_status)
str(A)
# 将类别标签转换为one-hot编码
train_y <- to_categorical(A$Recurrence_status[trainindex], num_classes = 2)

# 输出train_y以确认结果
print(train_y)