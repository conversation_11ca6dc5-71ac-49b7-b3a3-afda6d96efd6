library(survival)
td=read.table("UniCoxSurSigGeneExp.txt",header=T,sep="\t",check.names=F)    
td$surtime=td$surtime/12    #数据中的time单位是月，改成年

library(survivalROC)

#主要是用survivalROC函数，开始分析：
par(mar= c(5,5,1,1),cex.lab=1.2,cex.axis= 1.2) #先设置一下图形的边界
sROC=survivalROC(Stime=td$surtime, # 生存时间
                 status=td$surstat, # 生存状态
                 marker = td$gene87, #选择gene87
                 predict.time =5, # 看5年的时间段
                 method="KM")
plot(sROC$FP, sROC$TP, type="l", xlim=c(0,1), ylim=c(0,1),col="red", 
     xlab="False positive rate", ylab="True positive rate",
     lwd = 2, cex.main=1.3, cex.lab=1.5, cex.axis=1.2, font=1.2)
abline(0,1)
aucText=paste0("5 years"," (AUC=",sprintf("%.3f",sROC$AUC),")")
legend("bottomright", aucText,
       lwd=2,bty="n",col=c("red","green","blue"),cex=1.2)

par(mar= c(5,5,1,1),cex.lab=1.2,cex.axis= 1.2)

######多时间点分析######
#先画一个1年的图
sROC=survivalROC(Stime=td$surtime, status=td$surstat, marker = td$gene87, predict.time =1, method="KM")
plot(sROC$FP, sROC$TP, type="l", xlim=c(0,1), ylim=c(0,1),col="red", 
     xlab="False positive rate", ylab="True positive rate",
     lwd = 2, cex.main=1.3, cex.lab=1.5, cex.axis=1.2, font=1.2)
abline(0,1)
aucText=paste0("1 years"," (AUC=",sprintf("%.3f",sROC$AUC),")") #这个后面添加legend用

#再加一个3年的线
sROC3=survivalROC(Stime=td$surtime, status=td$surstat, marker = td$gene87, predict.time =3, method="KM")
lines(sROC3$FP, sROC3$TP, type="l", xlim=c(0,1), ylim=c(0,1),col="green",lwd = 2)
aucText3=paste0("3 years"," (AUC=",sprintf("%.3f",sROC3$AUC),")") #这个后面添加legend用

#再加一个5年的线
sROC5=survivalROC(Stime=td$surtime, status=td$surstat, marker = td$gene87, predict.time =5, method="KM")
lines(sROC5$FP, sROC5$TP, type="l", xlim=c(0,1), ylim=c(0,1),col="blue",lwd = 2)
aucText5=paste0("5 years"," (AUC=",sprintf("%.3f",sROC5$AUC),")") #这个后面添加legend用

#添加legend
legend("bottomright", c(aucText,aucText3,aucText5),
       lwd=2,bty="n",col=c("red","green","blue"),cex=1.2)
