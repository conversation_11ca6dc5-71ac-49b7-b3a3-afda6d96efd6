rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_new_seer1005.Rdata")
str(new_seer)
colnames(new_seer)

######所有变量######
#vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
#"Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup","Grade",
#"Depth_Of_Invasion","Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
#"Surgery","Surgery_Prim_Site","Elective_Neck_Surgery","Surgery_Of_DX",
#"Chemotherapy","Radiotherapy","Adjuvant_Radiotherapy","Treatment","CSS",
#"Survival.months","Vital_status","One_year_status","One_year_status_data",
#"Three_year_status","Three_year_status_data","Five_year_status",
#"Five_year_status_data","Ten_year_status","Ten_year_status_data",
#"Recurrence_time","Recurrence_status") 
######确定研究的变量，包括自变量、结果变量和协变量######
#Survival_m 和 Three_y_Death 作为响应变量，其余变量作为预测变量
#因变量y（机器学习中，y被称为“标签”，自变量被称为‘特征’）

# extract data for three year cohort
#table(new_seer$Three_year_status_data)
#threedata_full<-new_seer[new_seer$"Three_year_status_data" == 0,]
#threedata_full<-as.data.frame(threedata_full[,c('Three_year_status',
#                                                'Survival.months',
#                                                "Age","Sex","Marital_status",
#                                                "RaceGroup",
#                                                "household_income_group",
#                                                "Year_of_diagnosis",
#                                                "Primary_Site",
#                                                "Tumor_Size","Grade",
#                                                "Stage_T","Stage_N","Stage_M",
#                                                "Stage","Summary_stage",
#                                                "Surgery","Elective_Neck_Surgery",
#                                                "Surgery_Of_DX",
#                                                "Chemotherapy","Radiotherapy",
#                                                "Treatment")])
survdata_full<-as.data.frame(new_seer[,c('Vital_status',
                                                'Survival.months',
                                                "Age","Sex","Marital_status",
                                                "RaceGroup",
                                                "household_income_group",
                                                "Year_of_diagnosis",
                                                "Primary_Site",
                                                "Tumor_Size","Grade",
                                                "Stage_T","Stage_N","Stage_M",
                                                "Stage","Summary_stage",
                                                "Surgery","Elective_Neck_Surgery",
                                                "Surgery_Of_DX",
                                                "Chemotherapy","Radiotherapy",
                                                "Treatment")])
survdata <-na.omit(survdata_full)
survdata <-na.omit(survdata_full)
survdata<-droplevels(survdata)
summary(survdata)#Three_year_status的值需为0和1，注意因子化后值变为1和2
str(survdata)
survdata$Vital_status <- ifelse(survdata$Vital_status ==
                                      "Dead", 1, 0)
#########导入包##################
#remotes::install_github("mlr-org/mlr3learners")
#remotes::install_github("mlr-org/mlr3verse")
library(readxl)
library(table1)
library(survival)
library(survivalROC)
library(survminer)
library(rms)
library(CoxBoost)
library(magicfor)
library(randomForestSRC)
library(pec)
library(partykit)
library(party)
library(mlr3)
library(mlr3proba)
library(mlr3tuning)
library(magicfor)
library(mlr3learners)
library(mlr3extralearners)
library(mlr3verse)
library(paradox)

seeds<-c(1:10)
skimr::skim(survdata)
#观察数据
library(dplyr)
glimpse(survdata) # 数据类别
dim(survdata) # 数据维数
#########生存分析1：决策树rpart#################
#划分数据集
#index <- sample(1:nrow(survdata), round(0.3*nrow(survdata)))
#test_set <- survdata[-index,]
#train_set <- survdata[index,]
task<-TaskSurv$new(id = "surv_task", backend = survdata, 
                   time = "Survival.months",
                   event = "Vital_status")
split = partition(task,ratio = 0.8)  # 默认stratify = TRUE
#按 80% 和 20% 的比例划分为训练集和测试集，默认情况下会进行分层抽样
task
# 定义要绘制的变量
library(gridExtra)
rhs_vars <- c("Age","Sex","Marital_status",
              "RaceGroup",
              "household_income_group",
              "Year_of_diagnosis",
              "Primary_Site",
              "Tumor_Size","Grade",
              "Stage_T","Stage_N","Stage_M",
              "Stage","Summary_stage",
              "Surgery","Elective_Neck_Surgery",
              "Surgery_Of_DX",
              "Chemotherapy","Radiotherapy",
              "Treatment")
# 创建一个空列表来存储图形
plots <- list()

# 循环绘制图形并存储到列表中
for (var in rhs_vars) {
  plots[[var]] <- autoplot(task, rhs = var)  # 存储图形对象
}

# 使用 grid.arrange 显示所有图形
grid.arrange(grobs = plots, ncol = 4)  # 设置每行显示2个图形

#cols = c("Stage", "Chemotherapy") 
#autoplot(task$clone()$select(cols), type = "pairs")#选择了 Stage 和 Chemotherapy 两列进行成对可视化
#autoplot(task$clone()$select(cols),, type = "duo")#绘制了双变量图

#处理缺失值
#查看缺失情况
task$missings()
#构建了一个处理缺失值的管道，包括指示缺失值的步骤和插补步骤
po_indicator = po("missind",
                  affect_columns = selector_type(c("numeric", "integer")), 
                  type = "numeric")
impute_step = gunion(list(po_indicator, po("imputehist"))) %>>% 
  po("featureunion") %>>% 
  po("imputeoor")
impute_step$plot(horizontal = TRUE)
#处理分类特征，使用了合并因子、固定因子和样本插补的步骤
cat_step = po("collapsefactors", 
              param_vals = list(no_collapse_above_prevalence = 0.03)) %>>%
  po("fixfactors") %>>%
  po("imputesample") 
cat_step$plot(horizontal = TRUE)
#选择学习器，并设置了一些超参数以便后续调优。
rpart = lrn("surv.rpart",           
            keep_model = TRUE,            
            cp = to_tune(p_dbl(0, 0.1)), 
            minsplit = to_tune(p_int(10, 20)), 
            maxdepth = to_tune(p_int(2, 10)))
rpart
#将插补步骤、分类特征处理步骤和学习器连接起来，形成一个完整的处理图
graph = impute_step %>>% cat_step %>>% rpart
graph$plot()
#转化为图学习器：
glrn = as_learner(graph)
#使用随机搜索进行超参数调优，采用 5 折交叉验证评估模型性能。
at = auto_tuner(
  tuner = tnr("random_search"),
  learner = glrn,
  resampling = rsmp("cv", folds = 5),
  measure = msr("surv.cindex"),
  term_evals = 10)
#在训练集上启动自动调参过程：
set.seed(123) 
at$train(task, row_ids = split$train)
#查看最优超参数：
at$tuning_result
#可视化超参数maxdepth和minsplit的变化对模型性能的影响：
autoplot(at$tuning_instance, type = "points", 
         cols_x = c("surv.rpart.maxdepth", "surv.rpart.minsplit"))
#将最佳超参数应用于学习器，并在训练集上重新训练模型。
glrn$param_set$values = at$tuning_result$learner_param_vals[[1]]    
glrn$train(task, row_ids = split$train)
model = glrn$base_learner()
str(model)

# 提取 rpart 模型
rpart_model <- model$model  # 访问模型
# 转换为 party 对象
library(partykit)
party_model <- as.party(rpart_model)

# 使用 ggparty 绘图
library(ggparty)
ggparty(party_model)
g <- ggparty(party_model)
ggsave("party_model_plot.png", plot = g)
# 或者使用 plot 函数
library(rpart)
library(rpart.plot)
plot(party_model)

######获取模型的特征重要性，并可视化######
model$importance()
importance_scores <- model$importance()
library(ggplot2)
# 将特征重要性转换为数据框
importance_df <- data.frame(Feature = names(importance_scores), Importance = importance_scores)
str(importance_df)
# 绘制特征重要性图
ggplot(importance_df, aes(x = reorder(Feature, Importance), y = Importance)) +
  geom_bar(stat = "identity") +
  coord_flip() +
  labs(title = "Feature-Importance", x = "Feature", y = "Importance") +
  theme_minimal()
######模型预测及评估在测试集上做预测######
pred = glrn$predict(task, row_ids = split$test)
pred
#评估模型性能
#我们看看有哪些指标:mlr3中的生存模型结果指标一共有四种：
#response:预测生存时间;distr : 预测的生存分布;lp : 线性预测结果指标;crank : 连续的风险指标
as.data.table(mlr_measures)[ task_type == "surv", c("key", "predict_type")]
#pred$confusion  # 混淆矩阵，适用于分类任务
#pred$score(msr("classif.acc"))       # 准确率，适用于分类任务
#pred$score(msr("classif.recall"))    # 召回率，适用于分类任务
#pred$score(msr("classif.auc"))       # AUC，适用于分类任务
# 计算 C-index
c_index = pred$score(msr("surv.cindex"))
print(paste("C-index:", c_index))

# 计算 Brier Score
#brier_score = pred$score(msr("surv.brier"))
#print(paste("Brier Score:", brier_score))

###### 生存曲线可视化######
library(survival)
library(survminer)

# 获取测试集的行索引
test_row_ids = split$test

# 提取测试集的真实生存时间和事件状态
test_task = task$filter(test_row_ids)
#行筛选filter方法类似dplyr工具包中的同名函数，用来筛选行（样本）：
#列选择select方法类似dplyr工具包中的同名函数，用来选择除响应变量以外的列（变量）：
#如task_mtcars$select(c("cyl", "disp")) 

# 获取目标变量
test_data = test_task$truth()

# 将生存时间和事件状态分开
# 假设事件状态是通过 "+" 符号来表示的
survival_months = as.numeric(gsub("\\+", "", test_data))  # 去掉 "+" 符号并转换为数值型
Vital_status = as.numeric(grepl("\\+", test_data))   # 事件状态，1 表示事件发生，0 表示未发生

# 创建生存对象
surv_object <- Surv(time = survival_months,
                    event = Vital_status)
# 从 survdata 中提取 Chemotherapy 变量
Surgery_values = survdata$Surgery[test_row_ids]

# 将 Chemotherapy 变量添加到 test_data
test_data <- data.frame(SurvivalMonths = survival_months, EventOccurred = Vital_status, Surgery = Surgery_values)

# 检查 test_data 的结构
str(test_data)
# 绘制 Kaplan-Meier 曲线
km_fit <- survfit(surv_object ~ 1) # 这里可以根据需要添加分组变量
km_fit <- survfit(surv_object ~ Surgery, data = test_data)
ggsurvplot(km_fit, test_data,
           title = "Survival curve",
           xlab = "Time(month)",
           ylab = "Survival probability",
           risk.table = TRUE) # 添加风险表




######生存分析2：coxph ######
#训练模型的同时，还可以预测模型，同样为一行代码！
p = lrn("surv.coxph")$train(task, split$train)$predict(task, split$test)
#我们看看有哪些指标:mlr3中的生存模型结果指标一共有四种：
#response:预测生存时间;distr : 预测的生存分布;lp : 线性预测结果指标;crank : 连续的风险指标
as.data.table(mlr_measures)[ task_type == "surv", c("key", "predict_type")]
#模型评估
p$score(msrs("surv.cindex"))
p$score(msrs("surv.brier"))
p$score(msrs("surv.calib_beta"))


######生存分析3:多个生存模型基准测试######
#设定学习器集
mlr_learners$keys()#获取支持的学习器
#learners = c(lrns(c("surv.coxph","surv.rpart","surv.rfsrc", "surv.kaplan")))
# 单独创建每个学习器并设置参数
learner_coxph = lrns("surv.coxph")
learner_rpart = lrns("surv.rpart")
learner_rfsrc = lrns("surv.rfsrc")
learner_svm = lrns("surv.svm", gamma.mu = 0.1)  # 设置 gamma.mu 参数
learner_kaplan = lrns("surv.kaplan")
# 将所有学习器组合成一个列表
learners = c(learner_coxph, learner_rpart, learner_rfsrc, learner_kaplan)
# 基准测试
bmr = benchmark(benchmark_grid(task,  
                               learners,  
                               rsmp("cv", folds = 3)))
#设置多个评估指标
msr_txt = c("surv.rcll", "surv.cindex", "surv.dcalib") # 评估指标
measures = msrs(msr_txt)
#基准测试,使用aggregate()字段可计算评估指标的平均值，默认为“宏平均”，即所有中间模型评估分数的平均值：
bmr$aggregate(measures)[, c("learner_id", ..msr_txt)]
bmr$score(msr("surv.cindex"))
#箱线图可视化，在mlr3，你可以实现万物皆一行
autoplot(bmr, measure = msr("surv.cindex"))

######生存分析4:coxph回归生存分析）######
magic_for(print, silent = TRUE)
for (k in seeds){
  set.seed(k)
  index <- sample(1:nrow(survdata), round(0.3*nrow(survdata)))
  test_set <- survdata[-index,]
  train_set<- survdata[index,]
  task<-TaskSurv$new(id = "surv_task", backend = train_set, time = "Survival.months",
                     event = "Vital_status")
  cox.lrn <- lrn("surv.coxph")
  cox.lrn$train(task)
  train_pred<-cox.lrn$predict(task)
  train_p<-train_pred$score(msr("surv.cindex"))
  test_task <- TaskSurv$new(id = "test_task", backend = test_set, 
                            time = "Survival.months", event = "Vital_status")
  test_pred<-cox.lrn$predict(test_task)
  test_p<-test_pred$score(msr("surv.cindex"))
  print(round(train_p,3),round(test_p,3))
}

######根据error信息调整变量选择######
#threedata_full<-as.data.frame(threedata_full[,c('Three_year_status',
#                   'Survival.months',
#                   "Age","Sex","Marital_status","RaceGroup",
#                   "household_income_group",
#                   "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
#                   "Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
#                   "Surgery","Elective_Neck_Surgery","Surgery_Of_DX",
#                   "Chemotherapy","Radiotherapy","Treatment")])
#threedata <-na.omit(threedata_full)
#threedata<-droplevels(threedata)
#summary(threedata)
##

performance<-magic_result_as_dataframe()
summary(performance)

######生存分析5: surv.rpart in complete cases（决策树生存分析）######
magic_for(print, silent = TRUE)

for (k in seeds){
  set.seed(k)
  index <- sample(1:nrow(survdata), round(0.3*nrow(survdata)))
  test_set <- survdata[-index,]
  train_set <- survdata[index,]
  task <- TaskSurv$new(id = "surv_task", backend = train_set, 
                       time = "Survival.months",
                       event = "Vital_status")# 创建生存任务
  learner <- lrn("surv.rpart") # 创建学习器
  param_set <- learner$param_set
  print(param_set)
  # 定义参数集合
  ps = ps(
    minsplit  = p_int(lower = 1, upper = 20),
    maxdepth  = p_int(lower = 1, upper = 30)
  )
  # Tune model to find best performing parameter settings using random search algorithm
  instance <- TuningInstanceBatchSingleCrit$new(
    learner = learner,
    task = task,
    resampling = rsmp("cv", folds = 3),
    measure = msr("surv.cindex"),
    search_space = ps,
    terminator = trm("evals", n_evals = 20)
  )
  # 执行调优
  tuner <- tnr("random_search")
  tuner$optimize(instance)
  # Apply optimal parameters to model
  learner$param_set$values <- instance$result_learner_param_vals
  # 训练模型
  learner$train(task)
  # 预测和性能评估
  train_pred <- learner$predict(task)
  train_p <- train_pred$score(msr("surv.cindex")) # c-index  in training set
  test_task <- TaskSurv$new(id = "test_task", backend = test_set, 
                            time = "Survival.months", event = "Vital_status")
  test_pred <- learner$predict(test_task)
  test_p <- test_pred$score(msr("surv.cindex")) # 
  print(round(train_p,3),round(test_p,3))
}

performance<-magic_result_as_dataframe()
summary(performance)

######生存分析6:surv.randomForestSRC in complete cases######

#library(pracma)
#mlr_learners$keys()

# 初始化性能结果存储
magic_for(print, silent = TRUE)

for (k in 1:10) {
  set.seed(k)
  index <- sample(1:nrow(survdata), round(0.3 * nrow(survdata)))
  train_set <- survdata[index, ]
  test_set <- survdata[-index, ]
  
  # 创建生存任务
  task <- TaskSurv$new(id = "surv_task", backend = train_set, 
                       time = "Survival.months",
                       event = "Vital_status")
  
  # 创建学习器
  learner <- lrn("surv.rfsrc")
  print(learner)
  # 定义参数集合
  #  getParamSet("surv.rfsrc")
  #  learner$param_set
  ps = ps(
    ntree  = p_int(lower = 10, upper = 100),
    mtry  = p_int(lower = 1, upper = 3),
    nsplit  = p_int(lower = 0, upper = 10),
    splitrule = p_fct(c("logrank", "logrankscore", "random"))
  )
  
  # 定义调优实例
  instance <- TuningInstanceBatchSingleCrit$new(
    task = task,
    learner = learner,
    resampling = rsmp("cv", folds = 3),
    measure = msr("surv.cindex"),
    search_space = ps,
    terminator = trm("evals", n_evals = 20)
  )
  
  # 执行调优
  tuner <- tnr("random_search")
  tuner$optimize(instance)
  
  # 应用最佳参数
  learner$param_set$values <- instance$result_learner_param_vals
  
  # 训练模型
  learner$train(task)
  
  # 预测和性能评估
  train_pred <- learner$predict(task)
  train_p <- train_pred$score(msr("surv.cindex"))
  
  test_task <- TaskSurv$new(id = "test_task", backend = test_set, 
                            time = "Survival.months", 
                            event = "Vital_status")
  test_pred <- learner$predict(test_task)
  test_p <- test_pred$score(msr("surv.cindex"))
  
  # 存储性能结果
  print(round(train_p,3),round(test_p,3))
}

# 获取性能结果数据框
performance <- magic_result_as_dataframe()
summary(performance)

###########生存分析7:DeepSurv（深度学习生存分析）#################
library(mlr3verse)
library(mlr3extralearners)
library(mlr3proba)
library(mlr3measures)
requireNamespace("mlr3measures")
library(pseudo)
library(survivalmodels)
library(skimr)
library(distr6)
#install_pycox(
#  method = "auto",
#  conda = "auto",
#  pip = TRUE,
#  install_torch = FALSE,
#)
library(reticulate)
py_config()


magic_for(print, silent = TRUE)

#查看mlr3verse的学习器及依赖包
#as.data.table(lrns())[task_type=="surv"][,c("key","packages")]
#threedata$Vital_status <- as.integer(threedata$Vital_status)
for (k in 1:10) {
  set.seed(k)
  index <- sample(1:nrow(survdata), round(0.3 * nrow(survdata)))
  train_set <- survdata[index, ]
  test_set <- survdata[-index, ]
  skimr::skim(train_set)
  # 创建生存任务
  sur_task <- TaskSurv$new(id = "surv_task", backend = train_set,
                           time = "Survival.months",
                           event = "Vital_status")
  # library("mlr3viz") # 使用此包可视化数据
  # autoplot(sur_task, type = "pairs") 
  # 创建学习器
  lrn_deep = lrn("surv.deepsurv",optimizer = "adam")
  graph_deep = po("encode") %>>% lrn_deep
  graphlearner_deep = as_learner(graph_deep)
  
  # 定义参数集合
  print(graphlearner_deep$param_set)
  graphlearner_deep$param_set
  lrn_deep$param_set
  search_space = ps(
      neurons  = p_int(lower = 10, upper = 100),
      layers  = p_int(lower = 1, upper = 2),
      learning_rate = p_dbl(lower = 1e-4, upper = 0.1),
      dropout = p_dbl(lower = 0, upper = 0.5)
  )
  measures <- msrs(c("surv.cindex"))
  # measures <- msrs(c("surv.brier", 
  #                     "surv.cindex", 
  #                     "surv.calib_alpha",    
  #                     "surv.graf",       
  #                     "surv.rcll"))
  # 定义调优实例
  instance <- TuningInstanceBatchSingleCrit$new(  
    task = sur_task,
    learner = graphlearner_deep ,
    resampling = rsmp("cv", folds = 3),
    search_space = search_space,
    measure = measures,
    terminator = trm("evals", n_evals = 30)
  )
  # 执行调优
  tuner = tnr("random_search")
  tuner$optimize(instance)
  instance$result_learner_param_vals
  # 应用最佳参数
  graphlearner_deep$param_set$values = instance$result_learner_param_vals
  # 训练模型
  graphlearner_deep$train(sur_task)
  # 预测和性能评估
  train_pred <- learner$predict(task)
  train_p <- train_pred$score(msr("surv.cindex"))
  train_p <- train_pred$score(measure = measures)
  
  test_task <- TaskSurv$new(id = "test_task", backend = test_set, 
                            time = "Survival.months", 
                            event = "Vital_status")
  test_pred <- learner$predict(test_task)
  test_p <- test_pred$score(msr("surv.cindex"))
  
  # 存储性能结果
  print(round(train_p,3),round(test_p,3))
}

# 获取性能结果数据框
performance <- magic_result_as_dataframe()
summary(performance)

###############生存分析8:Cforest##################

magic_for(print, silent = TRUE)
table(threedata$Sex)
threedata$Sex=ifelse(threedata$Sex=='Female',0,1)

for (k in seeds) {
  set.seed(k)
  index <- sample(1:nrow(threedata), round(0.3*nrow(threedata)))
  train_set <- threedata[index,]
  test_set <- threedata[-index,]
  train_set <- na.omit(train_set)
  test_set <- na.omit(test_set)
  
  #  common <- intersect(names(train_set), names(test_set)) 
  #  for (p in common) { 
  #    if (class(train_set[[p]]) == "factor") { 
  #      levels(test_set[[p]]) <- levels(train_set[[p]]) 
  #      levels(train_set[[p]]) <- levels(test_set[[p]]) 
  # print(levels(test_set[[p]]))
  #    } 
  #  }
  task<-makeSurvTask(data = train_set,
                     target=c('Survival.months','Vital_status'))
  cif.lrn<-makeLearner(cl='surv.cforest',
                       predict.type = 'response')
  getParamSet("surv.cforest")
  model_params_4<-makeParamSet(
    makeIntegerParam('ntree', lower=10,upper=15),
    makeIntegerParam('mtry',lower = 6,upper = 10))
  
  # Tune model to find best performing parameter settings using random search algorithm
  tuned_model_4 <- tuneParams(learner = cif.lrn,
                              task = task,
                              resampling = rdesc,
                              measures =  cindex,       #  performance measure, this can be changed to one or many
                              par.set = model_params_4,
                              control = random_tune,
                              show.info = TRUE)
  # Apply optimal parameters to model
  rfsrc.lrn <- setHyperPars(learner = rfsrc.lrn,
                            par.vals = tuned_model_4$x)
  modrfsrc = train(rfsrc.lrn, task)
  train_pred_rfsrc<-predict(modrfsrc, newdata = train_set)
  train_p<-performance(train_pred_rfsrc, measures = list(cindex)) # c-index  in training set
  test_pred_rfsrc<-predict(modrfsrc, newdata = test_set) #prediction in test set
  test_p<-performance(test_pred_rfsrc, measures = list(cindex)) #  
  print(round(train_p,3),round(test_p,3))
}
performance<-magic_result_as_dataframe()
summary(performance)


############## 生存分析9:MI，缺失数据插补############  
## impute missing data for three year cohort using random forest ##

f <- as.formula(Surv(Survival_m, Three_y_Death) ~ .)

threedata_MI <- randomForestSRC::impute.rfsrc(f, data=threedata_full, 
                                              splitrule = "random", 
                                              na.action='na.impute',
                                              nimpute = 5)

## impute missing data for three year cohort using smcfcs ##
install.packages('smcfcs')
library(smcfcs)
smformula <- paste("Surv(Survival_m,Three_y_Death)~Age+Sex+Race+Marital_s",
                   "+Grade+T_n+N_n+M_n+Stage+LN_r+TS_n+Surgery+ICD_n")
threedata_smcfcs <- smcfcs(threedata_full, 
                           smtype = 'coxph',
                           smformula = smformula,
                           method = c('','','','mlogit','mlogit','mlogit',
                                      'podds','podds','podds','podds','podds',
                                      'mlogit','podds','mlogit','mlogit'),
                           m = 5,
                           numit = 5)
threedata_smcfcs<-smcfcs(threedata_full, smtype = 'coxph',
                         smformula = "Surv(Survival_m,Three_y_Death)~Age+Sex+Race+Marital_s
                         + Grade + T_n + N_n + M_n + Stage + LN_r + TS_n + Surgery + ICD_n",
                         method = c('','','','mlogit','mlogit','mlogit',
                                    'podds','podds','podds','podds','podds',
                                    'mlogit','podds','mlogit','mlogit'),
                         m=5,numit = 5) 

data=subset(threedata,select=c('Survival_m','Three_y_Death','Age','Sex'))
threedata_smcfcs<-smcfcs(data, smtype = 'coxph',
                         smformula = "Surv(Survival_m,Three_y_Death)~ Age+Sex",
                         method = c('','','mlogit','mlogit')) 

table(threedata$Age)
############生存分析10:pec校准图绘制###############
# calibration plot for three year cohort (complete cases, training set)
library(rpart)
library(pec)
library(party)
set.seed(19910220)
summary(survdata)
index1 <- sample(1:nrow(survdata), round(0.8*nrow(survdata)))
train_set_1 <- survdata[index1,]
test_set_1 <- survdata[-index1,]
summary(test_set_1)
sum(is.na(train_set_1))
sum(is.na(test_set_1))
cox1 <- coxph(Surv(Survival.months, Vital_status) ~., x = TRUE, y = TRUE, 
              data = train_set_1)
tree1 <- rpart(Surv(Survival.months, Vital_status)~.,
               data=train_set_1)
rsf1 <- rfsrc(Surv(Survival.months, Vital_status)~.,
              data=train_set_1,
              ntree=100,forest=TRUE,
              tree.err=T, importance=T,
              na.action = "na.impute")
bst1 <- pecCforest(Surv(Survival.months, Vital_status)~.,
                   data=train_set_1)
cf1=calPlot(list(cox1,rsf1,tree1),
            col=c("black",'green','red'),
            time=36,
            type="survival",
            legend=F, 
            data = test_set_1,
            splitMethod = "cv",
            B=10
)
plot(bst1)
cf1=calPlot(list(cox1 ,rsf1,tree1,bst1),
            col=c("black",'green','red','blue'),
            time=36,
            type="survival",
            legend=F, 
            data = test_set_1,
            splitMethod = "cv",
            B=10
)
legend("topleft", legend=c("Cox","Tree","randomForestSRC"),
       col=c("black",'red','green'), lty=1, cex=0.8)
cf1=calPlot(bst1,
            col=c("black"),
            time=36,
            type="survival",
            legend=F, 
            data = test_set_1,
            splitMethod = "cv",
            B=10
)
# overtime cindex using pec package ###
# overtime C index in complete cases for three year cohort (training set)
ApparrentCindex1 <- pec::cindex(list(cox1,                    
                                     tree1,
                                     rsf1),
                                formula=Surv(Survival_m, Three_y_Death) ~ .,
                                data=train_set_1,
                                splitMethod="cv",
                                B=10,
                                eval.times=seq(0,36,1))
plot(ApparrentCindex1,legend=c(2,1),xlim=c(0,36))
legend("topright", legend=c("Cox","Tree","randomForestSRC"),
       col=c("black",'red','green'), lty=1, cex=0.8)
plot(tree1)
library(prodlim)
library(survival)
###########
PredError <- pec(object=list(cox1 ,rsf1,tree1,bst1),
                 formula=Surv(Survival_m, Three_y_Death) ~ .,
                 data=train_set_1,
                 exact=TRUE,
                 cens.model="marginal",
                 splitMethod="none",
                 B=0,
                 verbose=TRUE)

print(PredError,times=seq(5,30,5))
summary(PredError)
plot(PredError)




####time consumption###
setseed(19910220)
task<-makeSurvTask(data = train_set_1,
                   target=c('Survival_m','Five_y_Death'))
lrns = list(makeLearner("surv.coxph"),
            makeLearner("surv.rpart"),
            makeLearner("surv.randomForestSRC"))
bmr = benchmark(lrns, task, rdesc, timetrain,show.info = FALSE)
perf = getBMRPerformances(bmr, as.df = TRUE)
sum(perf$timetrain[1:10]) # traning time for one iteration for Cox
sum(perf$timetrain[11:20])# traning time for one iteration for Tree
sum(perf$timetrain[21:30])# traning time for one iteration for RF

