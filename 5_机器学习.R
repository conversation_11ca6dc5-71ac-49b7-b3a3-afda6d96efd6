rm(list = ls())
setwd('/Volumes/备份/Documents/孟德尔随机化/SEER数据库/tongue')
load("tongue_new_seer1005.Rdata")
str(new_seer)
colnames(new_seer)

######所有变量######
#vars<-c("Age","Sex","Marital_status","RaceGroup","household_income_group",
#"Year_of_diagnosis","Primary_Site","Tumor_Size","GradeGroup","Grade",
#"Depth_Of_Invasion","Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
#"Surgery","Surgery_Prim_Site","Elective_Neck_Surgery","Surgery_Of_DX",
#"Chemotherapy","Radiotherapy","Adjuvant_Radiotherapy","Treatment","CSS",
#"Survival.months","Vital_status","One_year_status","One_year_status_data",
#"Three_year_status","Three_year_status_data","Five_year_status",
#"Five_year_status_data","Ten_year_status","Ten_year_status_data",
#"Recurrence_time","Recurrence_status") 
######确定研究的变量，包括自变量、结果变量和协变量######
#Survival_m 和 Three_y_Death 作为响应变量，其余变量作为预测变量
#因变量y（机器学习中，y被称为“标签”，自变量被称为‘特征’）

# extract data for three year cohort
table(new_seer$Three_year_status_data)
threedata_full<-new_seer[new_seer$"Three_year_status_data" == 0,]
threedata_full<-as.data.frame(threedata_full[,c('Three_year_status',
               "Age","Sex","Marital_status","RaceGroup","household_income_group",
               "Year_of_diagnosis","Primary_Site","Tumor_Size","Grade",
               "Stage_T","Stage_N","Stage_M","Stage","Summary_stage",
               "Surgery","Elective_Neck_Surgery","Surgery_Of_DX",
               "Chemotherapy","Radiotherapy","Treatment")])
threedata <-na.omit(threedata_full)
threedata<-droplevels(threedata)
summary(threedata)#Three_year_status的值需为0和1，注意因子化后值变为1和2
str(threedata)
#threedata <- select(threedata,1,3:22)
#########导入包#################
#remotes::install_github("mlr-org/mlr3proba")
#remotes::install_github("mlr-org/mlr3learners")
#install_github("cran/pracma")
library(readxl)
library(table1)
library(survival)
library(survivalROC)
library(survminer)
library(rms)
library(CoxBoost)
library(magicfor)
library(randomForestSRC)
library(pec)
library(partykit)
library(party)
library(mlr3)
library(mlr3proba)
library(mlr3tuning)
library(magicfor)
library(mlr3learners)
library(mlr3extralearners)
library(mlr3verse)
library(paradox)
library(precrec)
library(kernelshap) 
library(shapviz)

seeds<-c(1:10)
skimr::skim(threedata)
#观察数据
library(dplyr)
glimpse(threedata) # 数据类别
dim(threedata) # 数据维数

#构建任务
threedata$Three_year_status <- as.factor(threedata$Three_year_status)
task<-TaskClassif$new(id = "threedata_task", backend = threedata, 
                      target = "Three_year_status")
task
task$positive <- "1"#定义了“positive”类
#autoplot(task)
#cols = c("Sex", "Age") 
#autoplot(task$clone()$select(cols), type = "pairs")
#划分训练集和测试集
set.seed(123) 
split = partition(task,ratio = 0.8)  # 默认stratify = TRUE
#按 80% 和 20% 的比例划分为训练集和测试集，默认情况下会进行分层抽样
######特征工程，数据清洗######
task$missings()
#构建了一个处理缺失值的管道，包括指示缺失值的步骤和插补步骤
po_indicator = po("missind",
                  affect_columns = selector_type(c("numeric", "integer")), 
                  type = "numeric")
impute_step = gunion(list(po_indicator, po("imputehist"))) %>>% 
  po("featureunion") %>>% 
  po("imputeoor")
impute_step$plot(horizontal = TRUE)
#处理分类特征，使用了合并因子、固定因子和样本插补的步骤
cat_step = po("collapsefactors", 
              param_vals = list(no_collapse_above_prevalence = 0.03)) %>>%
  po("fixfactors") %>>%
  po("imputesample") 
cat_step$plot(horizontal = TRUE)
######选择学习器，并设置了一些超参数以便后续调优######
rpart_learner = lrn("classif.rpart", predict_type = "prob",  #每个类别的概率向量，即“后验概率”          
            keep_model = TRUE,            
            cp = to_tune(p_dbl(0, 0.1)), 
            minsplit = to_tune(p_int(10, 20)), 
            maxdepth = to_tune(p_int(2, 10)))
rpart_learner
#将插补步骤、分类特征处理步骤和学习器连接起来，形成一个完整的处理图
graph =  cat_step %>>% rpart_learner
graph$plot()
#转化为图学习器：
glrn = as_learner(graph)
#使用随机搜索进行超参数调优，采用 5 折交叉验证评估模型性能。
at = auto_tuner(
  tuner = tnr("random_search"),
  learner = glrn,
  resampling = rsmp("cv", folds = 5),
  measure = msr("classif.auc"),
  term_evals = 10)
#在训练集上启动自动调参过程：
print(task$col_roles)
set.seed(123) 
at$train(task, row_ids = split$train)
#查看最优超参数：
at$tuning_result
#可视化超参数maxdepth和minsplit的变化对模型性能的影响：
autoplot(at$tuning_instance, type = "points", 
         cols_x = c("classif.rpart.maxdepth", "classif.rpart.minsplit"))
#将最佳超参数应用于学习器，并在训练集上重新训练模型。
glrn$param_set$values = at$tuning_result$learner_param_vals[[1]]    
glrn$train(task, row_ids = split$train)

#R的SHAP解释，目前应用的包是shapviz，这个包仅能对Xgboost、LightGBM以及H2O模型进行解释，其余的机器学习模型并不适用。
#s <- kernelshap(glrn,X=threedata,bg_X=threedata)

#可视化学习器
model = glrn$base_learner()
autoplot(model, type = "ggparty")
model$importance()
str(model)
#模型预测及评估
pred = glrn$predict(task, row_ids = split$test)
pred
autoplot(pred)
pred$confusion   # 混淆矩阵
#pred$set_threshold(0.7)#设置阈值
#pred$score(msr("classif.acc"))

pred$score(msr("classif.acc")) # 准确率
pred$score(msr("classif.recall"))    # 召回率
pred$score(msr("classif.auc"))       # AUC
autoplot(pred, type = "roc")         # ROC曲线
autoplot(pred, type = "prc")         # PR曲线
#预测新数据取出任务中的从"use"移出的数据作为预测的新数据：
#newdata = task$data(rows = task$row_roles$holdout)
#glrn$predict_newdata(newdata)



######结合多种机器学习模型预测结果######
#构建学习器
learner_logreg = lrn("classif.log_reg", predict_type = "prob",id="logreg")

learner_rf = lrn("classif.ranger", num.trees = 500,
                 predict_type = "prob",
                 mtry=to_tune(15,30),
                 min.node.size=to_tune(5,12),
                 max.depth=to_tune(3,9),
                 id="ranger")

learner_svm=lrn("classif.svm",predict_type="prob",
                type="C-classification",
                cost=to_tune(0.1,10),
                gamma=to_tune(0.1,10),
                kernel=to_tune(c("polynomial","radial","sigmoid")),
                degree=to_tune(1,3),
                id="svm")

learner_xgb=lrn("classif.xgboost",predict_type="response",
                eta=to_tune(0,1),
                gamma=to_tune(0,5),
                max_depth=to_tune(1,8),
                min_child_weight=to_tune(1,10),
                subsample=to_tune(0.5,1),
                colsample_bytree=to_tune(0.5,1),
                nrounds=to_tune(20,30),
                eval_metric=to_tune(c("merror","mlogloss")),
                id="xgboost")

learners = list(learner_logreg,learner_rf,learner_svm,learner_xgb)
#使用PipeOp Branch从给定的图形中创建不同的路径分支，其中只有一支分支被评估
granp<-ppl("branch",learners)
granp$plot()
#转化为图学习器
glearner<-as_learner(granp)
glearner

#用auto_tuner()做嵌套重抽样调参
future::plan("multisession")
at<-auto_tuner(tuner = tnr("random_search"),
               learner = glearner,
               resampling = rsmp("cv",folds=3),
               measure = msr("classif.ce"),
               term_evals = 10)

rr<-resample(task,at,rsmp("cv",folds=4),store_models = TRUE)
rr$aggregate()
extract_inner_tuning_results(rr)[,1:17]
extract_inner_tuning_archives(rr)[990:1000,1:17]
future::plan("multisession")
at$train(task)
at$tuning_result$learner_param_vals[[1]] #最优超参数





######随机森林分析######
######随机森林分析-1、数据和任务######
######随机森林分析-2、学习器和算法######
#查看可用的学习器
lrns()

#使用随机森林算法
learners<-lrn('classif.ranger',num.trees=100,predict_type='prob',num.threads=5)
learners
#训练模型
learners$train(task=task,row_ids=split$train)
learners$model
#验证模型
prediction<-learners$predict(task=task,row_ids=split$test)
prediction
prediction$confusion
######随机森林分析-3、模型评价######
#查看可用的模型评价指标
msrs()

measure<-msr("classif.acc")
prediction$score(measure)#准确率

#绘制ROC曲线（2分类问题）
autoplot(prediction,type='roc')

######随机森林分析-4、重抽样######
#查看可用的重抽样方法
rsmps()

#创建任务
task<-TaskClassif$new(id = "threedata_task", backend = threedata, 
                      target = "Three_year_status")
task

#定义重抽样方案
resampling<-rsmp('cv',folds=10)
resampling

#查看重抽样结果
cv10<-resampling$instantiate(task=task)
cv10$iters#10次
cv10$train_set(1)
cv10$test_set(1)

#使用重抽样进行模型评价
my_resample<-resample(task=task,learner=learners,resampling=cv10,store_models=TRUE)
my_resample

#模型评价
#汇总
my_resample$aggregate(measures=measure) 

#单次抽样
my_resample$score(measures=measure)$classif.acc
my_resample$score(measures=measure)$classif.acc%>%mean()

my_resample$resampling$train_set(1)
my_resample$resampling$test_set(1)
my_resample$prediction()
my_resample$learners[[1]]$model

s <- kernelshap(learners,X=threedata,bg_X=threedata)

######基准测试######
######基准测试-1、数据和任务######
######基准测试-2、学习器和算法######

#查看可用的学习器
lrns()

learners=lrns(c("classif.rpart","classif.kknn","classif.ranger"
                ),
                predict_type="prob")
#选取多个学习器：决策树、KNN、随机森林、支持向量机
#（因为要计算AUC值，预测类型需要改为"prob"）

######基准测试-3、重抽样######
resampling<-rsmp('cv',folds=5)
resampling

######基准测试-4、基准测试######
print(task$feature_types)
#benchmark_grid
design=benchmark_grid(task,learners,resampling)

#执行基准测试
bmr=benchmark(design)

######基准测试-5、效果评价######
measures=list(msr("classif.acc"),msr("classif.auc"))

bmr$aggregate(measures)  #汇总基准测试结果

autoplot(bmr,measure=msr("classif.auc"))  #可视化








###### surv.rpart in complete cases（决策树生存分析）######
magic_for(print, silent = TRUE)

for (k in seeds){
  set.seed(k)
  index <- sample(1:nrow(threedata), round(0.3*nrow(threedata)))
  test_set <- threedata[-index,]
  train_set <- threedata[index,]
  task <- TaskSurv$new(id = "surv_task", backend = train_set, 
                       time = "Survival.months",
                       event = "Three_year_status")# 创建生存任务
  learner <- lrn("surv.rpart") # 创建学习器
  param_set <- learner$param_set
  print(param_set)
  # 定义参数集合
  ps = ps(
    minsplit  = p_int(lower = 1, upper = 20),
    maxdepth  = p_int(lower = 1, upper = 30)
  )
  # Tune model to find best performing parameter settings using random search algorithm
  instance <- TuningInstanceBatchSingleCrit$new(
    learner = learner,
    task = task,
    resampling = rsmp("cv", folds = 3),
    measure = msr("surv.cindex"),
    search_space = ps,
    terminator = trm("evals", n_evals = 20)
  )
  # 执行调优
  tuner <- tnr("random_search")
  tuner$optimize(instance)
  # Apply optimal parameters to model
  learner$param_set$values <- instance$result_learner_param_vals
  # 训练模型
  learner$train(task)
  # 预测和性能评估
  train_pred <- learner$predict(task)
  train_p <- train_pred$score(msr("surv.cindex")) # c-index  in training set
  test_task <- TaskSurv$new(id = "test_task", backend = test_set, 
                            time = "Survival.months", event = "Three_year_status")
  test_pred <- learner$predict(test_task)
  test_p <- test_pred$score(msr("surv.cindex")) # 
  print(round(train_p,3),round(test_p,3))
}

performance<-magic_result_as_dataframe()
summary(performance)

######surv.randomForestSRC in complete cases######

library(pracma)
#mlr_learners$keys()获取支持的学习器
# 初始化性能结果存储
magic_for(print, silent = TRUE)

for (k in 1:10) {
  set.seed(k)
  index <- sample(1:nrow(threedata), round(0.3 * nrow(threedata)))
  train_set <- threedata[index, ]
  test_set <- threedata[-index, ]
  
  # 创建生存任务
  task <- TaskSurv$new(id = "surv_task", backend = train_set, 
                       time = "Survival.months",
                       event = "Three_year_status")
  
  # 创建学习器
  learner <- lrn("surv.rfsrc")
  print(learner)
  # 定义参数集合
#  getParamSet("surv.rfsrc")
#  learner$param_set
  ps = ps(
    ntree  = p_int(lower = 10, upper = 100),
    mtry  = p_int(lower = 1, upper = 3),
    nsplit  = p_int(lower = 0, upper = 10),
    splitrule = p_fct(c("logrank", "logrankscore", "random"))
  )
  
  # 定义调优实例
  instance <- TuningInstanceBatchSingleCrit$new(
    task = task,
    learner = learner,
    resampling = rsmp("cv", folds = 3),
    measure = msr("surv.cindex"),
    search_space = ps,
    terminator = trm("evals", n_evals = 20)
  )
  
  # 执行调优
  tuner <- tnr("random_search")
  tuner$optimize(instance)
  
  # 应用最佳参数
  learner$param_set$values <- instance$result_learner_param_vals
  
  # 训练模型
  learner$train(task)
  
  # 预测和性能评估
  train_pred <- learner$predict(task)
  train_p <- train_pred$score(msr("surv.cindex"))
  
  test_task <- TaskSurv$new(id = "test_task", backend = test_set, 
                            time = "Survival.months", 
                            event = "Three_year_status")
  test_pred <- learner$predict(test_task)
  test_p <- test_pred$score(msr("surv.cindex"))
  
  # 存储性能结果
  print(round(train_p,3),round(test_p,3))
}

# 获取性能结果数据框
performance <- magic_result_as_dataframe()
summary(performance)

###########DeepSurv（深度学习生存分析）(几年存活率不能)#################
library(mlr3verse)
library(mlr3extralearners)
library(mlr3proba)
library(mlr3measures)
library(pseudo)
library(survivalmodels)
library(skimr)
library(distr6)
#install_pycox(
#  method = "auto",
#  conda = "auto",
#  pip = TRUE,
#  install_torch = FALSE,
#)
library(reticulate)
py_config()


magic_for(print, silent = TRUE)

#查看mlr3verse的学习器及依赖包
#as.data.table(lrns())[task_type=="surv"][,c("key","packages")]

for (k in 1:10) {
  set.seed(k)
  index <- sample(1:nrow(threedata), round(0.3 * nrow(threedata)))
  train_set <- threedata[index, ]
  test_set <- threedata[-index, ]
  skimr::skim(train_set)
  # 创建生存任务
  sur_task <- TaskSurv$new(id = "surv_task", backend = train_set,
                           time = "Survival.months",
                           event = "Three_year_status")
# library("mlr3viz") # 使用此包可视化数据
# autoplot(sur_task, type = "pairs") 
  # 创建学习器
  lrn_deep = lrn("surv.deepsurv",optimizer = "adam")
  graph_deep = po("encode") %>>% lrn_deep
  graphlearner_deep = as_learner(graph_deep)
  
  # 定义参数集合
  print(graphlearner_deep$param_set)
  graphlearner_deep$param_set
  lrn_deep$param_set
  search_space = ps()
  measures <- msrs(c("surv.cindex"))
# measures <- msrs(c("surv.brier", 
#                     "surv.cindex", 
#                     "surv.calib_alpha",    
#                     "surv.graf",       
#                     "surv.rcll"))
  # 定义调优实例
  instance <- TuningInstanceBatchSingleCrit$new(  
    task = sur_task,
    learner = graphlearner_deep ,
    resampling = rsmp("cv", folds = 3),
    search_space = search_space,
    measure = measures,
    terminator = trm("evals", n_evals = 30)
  )
  # 执行调优
  tuner = tnr("random_search")
  tuner$optimize(instance)
  instance$result_learner_param_vals
  # 应用最佳参数
  graphlearner_deep$param_set$values = instance$result_learner_param_vals
  # 训练模型
  graphlearner_deep$train(sur_task)
  # 预测和性能评估
  train_pred <- learner$predict(task)
  train_p <- train_pred$score(msr("surv.cindex"))
  train_p <- train_pred$score(measure = measures)
  
  test_task <- TaskSurv$new(id = "test_task", backend = test_set, 
                            time = "Survival.months", 
                            event = "Three_year_status")
  test_pred <- learner$predict(test_task)
  test_p <- test_pred$score(msr("surv.cindex"))
  
  # 存储性能结果
  print(round(train_p,3),round(test_p,3))
}

# 获取性能结果数据框
performance <- magic_result_as_dataframe()
summary(performance)

###############Cforest##################
detach("package:mlr3verse", unload = TRUE)
detach("package:mlr3extralearners", unload = TRUE)
detach("package:mlr3learners", unload = TRUE)
library(mlr)
magic_for(print, silent = TRUE)
table(threedata$Sex)
threedata$Sex=ifelse(threedata$Sex=='Female',0,1)

for (k in seeds) {
  set.seed(k)
  index <- sample(1:nrow(threedata), round(0.3*nrow(threedata)))
  train_set <- threedata[index,]
  test_set <- threedata[-index,]
  train_set <- na.omit(train_set)
  test_set <- na.omit(test_set)
  
#  common <- intersect(names(train_set), names(test_set)) 
#  for (p in common) { 
#    if (class(train_set[[p]]) == "factor") { 
#      levels(test_set[[p]]) <- levels(train_set[[p]]) 
#      levels(train_set[[p]]) <- levels(test_set[[p]]) 
      # print(levels(test_set[[p]]))
#    } 
#  }
  task<-makeSurvTask(data = train_set,
                     target=c('Survival.months','Three_year_status'))
  cif.lrn<-makeLearner(cl='surv.cforest',
                       predict.type = 'response')
  getParamSet("surv.cforest")
  model_params_4<-makeParamSet(
    makeIntegerParam('ntree', lower=10,upper=15),
    makeIntegerParam('mtry',lower = 6,upper = 10))
  
  # Tune model to find best performing parameter settings using random search algorithm
  tuned_model_4 <- tuneParams(learner = cif.lrn,
                              task = task,
                              resampling = rdesc,
                              measures =  cindex,       #  performance measure, this can be changed to one or many
                              par.set = model_params_4,
                              control = random_tune,
                              show.info = TRUE)
  # Apply optimal parameters to model
  rfsrc.lrn <- setHyperPars(learner = rfsrc.lrn,
                            par.vals = tuned_model_4$x)
  modrfsrc = train(rfsrc.lrn, task)
  train_pred_rfsrc<-predict(modrfsrc, newdata = train_set)
  train_p<-performance(train_pred_rfsrc, measures = list(cindex)) # c-index  in training set
  test_pred_rfsrc<-predict(modrfsrc, newdata = test_set) #prediction in test set
  test_p<-performance(test_pred_rfsrc, measures = list(cindex)) #  
  print(round(train_p,3),round(test_p,3))
}
performance<-magic_result_as_dataframe()
summary(performance)


############## MI 缺失数据插补############  
## impute missing data for three year cohort using random forest ##
#使用随机森林插补缺失数据
f <- as.formula(Surv(Survival_m, Three_y_Death) ~ .)

threedata_MI <- randomForestSRC::impute.rfsrc(f, data=threedata_full, 
                                              splitrule = "random", 
                                              na.action='na.impute',
                                              nimpute = 5)

## impute missing data for three year cohort using smcfcs ##
#使用SMCFCS插补缺失数据
install.packages('smcfcs')
library(smcfcs)
smformula <- paste("Surv(Survival_m,Three_y_Death)~Age+Sex+Race+Marital_s",
                   "+Grade+T_n+N_n+M_n+Stage+LN_r+TS_n+Surgery+ICD_n")
threedata_smcfcs <- smcfcs(threedata_full, 
                           smtype = 'coxph',
                           smformula = smformula,
                           method = c('','','','mlogit','mlogit','mlogit',
                                      'podds','podds','podds','podds','podds',
                                      'mlogit','podds','mlogit','mlogit'),
                           m = 5,
                           numit = 5)
threedata_smcfcs<-smcfcs(threedata_full, smtype = 'coxph',
                         smformula = "Surv(Survival_m,Three_y_Death)~Age+Sex+Race+Marital_s
                         + Grade + T_n + N_n + M_n + Stage + LN_r + TS_n + Surgery + ICD_n",
                         method = c('','','','mlogit','mlogit','mlogit',
                                    'podds','podds','podds','podds','podds',
                                    'mlogit','podds','mlogit','mlogit'),
                         m=5,numit = 5) 

data=subset(threedata,select=c('Survival_m','Three_y_Death','Age','Sex'))
threedata_smcfcs<-smcfcs(data, smtype = 'coxph',
                         smformula = "Surv(Survival_m,Three_y_Death)~ Age+Sex",
                         method = c('','','mlogit','mlogit')) 

table(threedata$Age)
############pec校准图绘制###############
# calibration plot for three year cohort (complete cases, training set)
library(rpart)
library(pec)
library(party)
set.seed(19910220)
index1 <- sample(1:nrow(threedata), round(0.8*nrow(threedata)))
train_set_1 <- threedata[index1,]
test_set_1 <- threedata[-index1,]
cox1 <- coxph(Surv(Survival_m, Three_y_Death) ~., x = T, y = T, 
              data = train_set_1)
tree1 <- rpart(Surv(Survival_m, Three_y_Death)~.,
               data=train_set_1)
rsf1 <- rfsrc(Surv(Survival_m, Three_y_Death)~.,
              data=train_set_1,
              ntree=100,forest=TRUE,
              tree.err=T, importance=T,
              na.action = "na.impute")
bst1 <- pecCforest(Surv(Survival_m, Three_y_Death)~.,
                   data=train_set_1)
cf1=calPlot(list(cox1 ,rsf1,tree1),
            col=c("black",'green','red'),
            time=36,
            type="survival",
            legend=F, 
            data = test_set_1,
            splitMethod = "cv",
            B=10
)
plot(bst1)
cf1=calPlot(list(cox1 ,rsf1,tree1,bst1),
            col=c("black",'green','red','blue'),
            time=36,
            type="survival",
            legend=F, 
            data = test_set_1,
            splitMethod = "cv",
            B=10
)
legend("topleft", legend=c("Cox","Tree","randomForestSRC"),
       col=c("black",'red','green'), lty=1, cex=0.8)
cf1=calPlot(bst1,
            col=c("black"),
            time=36,
            type="survival",
            legend=F, 
            data = test_set_1,
            splitMethod = "cv",
            B=10
)
# overtime cindex using pec package ###
# overtime C index in complete cases for three year cohort (training set)
#C-index计算
ApparrentCindex1 <- pec::cindex(list(cox1,                    
                                     tree1,
                                     rsf1),
                                formula=Surv(Survival_m, Three_y_Death) ~ .,
                                data=train_set_1,
                                splitMethod="cv",
                                B=10,
                                eval.times=seq(0,36,1))
plot(ApparrentCindex1,legend=c(2,1),xlim=c(0,36))
legend("topright", legend=c("Cox","Tree","randomForestSRC"),
       col=c("black",'red','green'), lty=1, cex=0.8)
plot(tree1)
library(prodlim)
library(survival)
######预测误差评估######
PredError <- pec(object=list(cox1 ,rsf1,tree1,bst1),
                 formula=Surv(Survival_m, Three_y_Death) ~ .,
                 data=train_set_1,
                 exact=TRUE,
                 cens.model="marginal",
                 splitMethod="none",
                 B=0,
                 verbose=TRUE)

print(PredError,times=seq(5,30,5))
summary(PredError)
plot(PredError)




####time consumption###
setseed(19910220)
task<-makeSurvTask(data = train_set_1,
                   target=c('Survival_m','Five_y_Death'))
lrns = list(makeLearner("surv.coxph"),
            makeLearner("surv.rpart"),
            makeLearner("surv.randomForestSRC"))
bmr = benchmark(lrns, task, rdesc, timetrain,show.info = FALSE)
perf = getBMRPerformances(bmr, as.df = TRUE)
sum(perf$timetrain[1:10]) # traning time for one iteration for Cox
sum(perf$timetrain[11:20])# traning time for one iteration for Tree
sum(perf$timetrain[21:30])# traning time for one iteration for RF

