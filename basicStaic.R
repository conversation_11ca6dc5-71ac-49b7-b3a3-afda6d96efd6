#存储数据
rm(list = ls())
save(data,file='./breat.Rdata')

#加载
load('Tongue_T1.Rdata')
attach(data)
data$Patient.ID
detach(data)

Patient.ID

##基本统计分析
#T test
table(Sex)
t.test(Regional.nodes.positive..1988.. ~ Sex)
#卡方检验2*2

chisq.test(Derived.HER2.Recode..2010..,Sex)

table(Derived.HER2.Recode..2010..,Sex)

table(Derived.AJCC.T..6th.ed..2004.2015.,Sex)
#fisher exact test 2*n
fisher.test(Derived.AJCC.T..6th.ed..2004.2015.,Sex)
table(Derived.AJCC.T..6th.ed..2004.2015.)



€
#直接绘制三线表
install.packages('table1')
library(table1)

table1(~factor(Sex)+Age.recode.with..1.year.olds+factor(Race.recode..W..B..AI..API.)+Regional.nodes.positive..1988..|Breast.Subtype..2010..,
       data=data,topclass="Rtable1-zebra")

table1(~Derived.AJCC.Stage.Group..6th.ed..2004.2015.+Age.recode.with..1.year.olds+factor(Race.recode..W..B..AI..API.)|Sex*Breast.Subtype..2010..,
       data=data, topclass="Rtable1-zebra")


#统计检验三线表

data$n_sex=ifelse(data$Sex=='Female',1,0)
table(data$n_sex)

data$n_sex <- factor(data$n_sex, levels=c(0, 1, 2), labels=c("male", "Female", "P-value"))

Age.recode.with..1.year.olds=factor(Age.recode.with..1.year.olds)
Race.recode..W..B..AI..API.=factor(Race.recode..W..B..AI..API.)


x_value=data$n_sex

rndr <- function(x, name, ...) {
  if (length(x) == 0) {
    y <- data[[name]]
    s <- rep("", length(render.default(x=y, name=name, ...)))
    if (is.numeric(y)) {
      p <- t.test(y ~ x_value)$p.value
    } else {
      p <- fisher.test(table(y, droplevels(x_value)))$p.value
    }
    s[2] <- sub("<", "&lt;", format.pval(p, digits=3, eps=0.001))
    s
  } else {
    render.default(x=x, name=name, ...)
  }
}
rndr.strat <- function(label, n, ...) {
  ifelse(n==0, label, render.strat.default(label, n, ...))
}
table(n_sex)

## 绘图
table1(~Breast.Subtype..2010..+ Age.recode.with..1.year.olds+Race.recode..W..B..AI..API.+Regional.nodes.positive..1988..|n_sex ,
       data=data, render=rndr,droplevels=F, render.strat=rndr.strat, overall=F, topclass="Rtable1-zebra")
